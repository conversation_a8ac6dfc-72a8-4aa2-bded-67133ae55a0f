<svg xmlns="http://www.w3.org/2000/svg" width="240" height="60" viewBox="0 0 240 60" fill="none">
  <!-- Background -->
  <rect width="240" height="60" rx="8" fill="url(#gradient)" />
  
  <!-- Decorative elements -->
  <circle cx="20" cy="30" r="8" fill="#FFD700" opacity="0.3" />
  <circle cx="220" cy="30" r="6" fill="#FF6B6B" opacity="0.3" />
  
  <!-- Main text -->
  <text x="30" y="25" font-family="serif" font-size="18" font-weight="bold" fill="#2C3E50">
    Thirumanam
  </text>
  
  <!-- 360 text -->
  <text x="160" y="25" font-family="sans-serif" font-size="16" font-weight="bold" fill="#E74C3C">
    360
  </text>
  
  <!-- Tagline -->
  <text x="30" y="42" font-family="sans-serif" font-size="10" fill="#7F8C8D">
    Your Dream Wedding Starts Here
  </text>
  
  <!-- Decorative heart -->
  <path d="M200 35 c-2-4 -8-4 -10 0 c-2-4 -8-4 -10 0 c0 3 5 8 10 12 c5-4 10-9 10-12 z" fill="#E74C3C" />
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FFF8DC;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFFACD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F0E68C;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
