/* eslint-disable */
// this is an auto generated file. This will be overwritten

export const createVendor = /* GraphQL */ `
  mutation CreateVendor(
    $input: CreateVendorInput!
    $condition: ModelVendorConditionInput
  ) {
    createVendor(input: $input, condition: $condition) {
      id
      userId
      name
      description
      contact
      email
      address
      city
      state
      pincode
      website
      category
      profilePhoto
      gallery
      services {
        name
        price
        description
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      experience
      events
      responseTime
      rating
      reviewCount
      verified
      featured
      availability
      priceRange
      specializations
      awards
      languages
      coverage
      equipment
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateVendor = /* GraphQL */ `
  mutation UpdateVendor(
    $input: UpdateVendorInput!
    $condition: ModelVendorConditionInput
  ) {
    updateVendor(input: $input, condition: $condition) {
      id
      userId
      name
      description
      contact
      email
      address
      city
      state
      pincode
      website
      category
      profilePhoto
      gallery
      services {
        name
        price
        description
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      experience
      events
      responseTime
      rating
      reviewCount
      verified
      featured
      availability
      priceRange
      specializations
      awards
      languages
      coverage
      equipment
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteVendor = /* GraphQL */ `
  mutation DeleteVendor(
    $input: DeleteVendorInput!
    $condition: ModelVendorConditionInput
  ) {
    deleteVendor(input: $input, condition: $condition) {
      id
      userId
      name
      description
      contact
      email
      address
      city
      state
      pincode
      website
      category
      profilePhoto
      gallery
      services {
        name
        price
        description
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      experience
      events
      responseTime
      rating
      reviewCount
      verified
      featured
      availability
      priceRange
      specializations
      awards
      languages
      coverage
      equipment
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createVenue = /* GraphQL */ `
  mutation CreateVenue(
    $input: CreateVenueInput!
    $condition: ModelVenueConditionInput
  ) {
    createVenue(input: $input, condition: $condition) {
      id
      userId
      name
      description
      type
      capacity
      location
      city
      state
      fullAddress
      pincode
      contactPhone
      contactEmail
      website
      price
      priceRange
      images
      amenities
      spaces {
        name
        capacity
        area
        price
        description
        amenities
        images
        __typename
      }
      packages {
        name
        price
        duration
        description
        includes
        excludes
        terms
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      rating
      reviewCount
      bookings
      verified
      featured
      status
      availability
      policies {
        cancellation
        advance
        catering
        decoration
        alcohol
        music
        parking
        __typename
      }
      coordinates {
        latitude
        longitude
        __typename
      }
      operatingHours {
        monday
        tuesday
        wednesday
        thursday
        friday
        saturday
        sunday
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateVenue = /* GraphQL */ `
  mutation UpdateVenue(
    $input: UpdateVenueInput!
    $condition: ModelVenueConditionInput
  ) {
    updateVenue(input: $input, condition: $condition) {
      id
      userId
      name
      description
      type
      capacity
      location
      city
      state
      fullAddress
      pincode
      contactPhone
      contactEmail
      website
      price
      priceRange
      images
      amenities
      spaces {
        name
        capacity
        area
        price
        description
        amenities
        images
        __typename
      }
      packages {
        name
        price
        duration
        description
        includes
        excludes
        terms
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      rating
      reviewCount
      bookings
      verified
      featured
      status
      availability
      policies {
        cancellation
        advance
        catering
        decoration
        alcohol
        music
        parking
        __typename
      }
      coordinates {
        latitude
        longitude
        __typename
      }
      operatingHours {
        monday
        tuesday
        wednesday
        thursday
        friday
        saturday
        sunday
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteVenue = /* GraphQL */ `
  mutation DeleteVenue(
    $input: DeleteVenueInput!
    $condition: ModelVenueConditionInput
  ) {
    deleteVenue(input: $input, condition: $condition) {
      id
      userId
      name
      description
      type
      capacity
      location
      city
      state
      fullAddress
      pincode
      contactPhone
      contactEmail
      website
      price
      priceRange
      images
      amenities
      spaces {
        name
        capacity
        area
        price
        description
        amenities
        images
        __typename
      }
      packages {
        name
        price
        duration
        description
        includes
        excludes
        terms
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      rating
      reviewCount
      bookings
      verified
      featured
      status
      availability
      policies {
        cancellation
        advance
        catering
        decoration
        alcohol
        music
        parking
        __typename
      }
      coordinates {
        latitude
        longitude
        __typename
      }
      operatingHours {
        monday
        tuesday
        wednesday
        thursday
        friday
        saturday
        sunday
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createShop = /* GraphQL */ `
  mutation CreateShop(
    $input: CreateShopInput!
    $condition: ModelShopConditionInput
  ) {
    createShop(input: $input, condition: $condition) {
      id
      userId
      name
      category
      price
      originalPrice
      discount
      stock
      sku
      brand
      featured
      description
      features
      sizes
      colors
      images
      specifications {
        fabric
        work
        occasion
        care
        delivery
        returnPolicy
        __typename
      }
      rating
      reviewCount
      inStock
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateShop = /* GraphQL */ `
  mutation UpdateShop(
    $input: UpdateShopInput!
    $condition: ModelShopConditionInput
  ) {
    updateShop(input: $input, condition: $condition) {
      id
      userId
      name
      category
      price
      originalPrice
      discount
      stock
      sku
      brand
      featured
      description
      features
      sizes
      colors
      images
      specifications {
        fabric
        work
        occasion
        care
        delivery
        returnPolicy
        __typename
      }
      rating
      reviewCount
      inStock
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteShop = /* GraphQL */ `
  mutation DeleteShop(
    $input: DeleteShopInput!
    $condition: ModelShopConditionInput
  ) {
    deleteShop(input: $input, condition: $condition) {
      id
      userId
      name
      category
      price
      originalPrice
      discount
      stock
      sku
      brand
      featured
      description
      features
      sizes
      colors
      images
      specifications {
        fabric
        work
        occasion
        care
        delivery
        returnPolicy
        __typename
      }
      rating
      reviewCount
      inStock
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createReview = /* GraphQL */ `
  mutation CreateReview(
    $input: CreateReviewInput!
    $condition: ModelReviewConditionInput
  ) {
    createReview(input: $input, condition: $condition) {
      id
      userId
      name
      email
      location
      weddingDate
      category
      rating
      title
      review
      wouldRecommend
      verified
      status
      entityType
      entityId
      userEntityComposite
      serviceRating
      valueRating
      communicationRating
      professionalismRating
      images
      helpfulCount
      purchaseVerified
      reviewHelpfulUsers
      vendorResponse
      responseDate
      reviewTarget
      adminNotes
      moderatedBy
      moderatedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateReview = /* GraphQL */ `
  mutation UpdateReview(
    $input: UpdateReviewInput!
    $condition: ModelReviewConditionInput
  ) {
    updateReview(input: $input, condition: $condition) {
      id
      userId
      name
      email
      location
      weddingDate
      category
      rating
      title
      review
      wouldRecommend
      verified
      status
      entityType
      entityId
      userEntityComposite
      serviceRating
      valueRating
      communicationRating
      professionalismRating
      images
      helpfulCount
      purchaseVerified
      reviewHelpfulUsers
      vendorResponse
      responseDate
      reviewTarget
      adminNotes
      moderatedBy
      moderatedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteReview = /* GraphQL */ `
  mutation DeleteReview(
    $input: DeleteReviewInput!
    $condition: ModelReviewConditionInput
  ) {
    deleteReview(input: $input, condition: $condition) {
      id
      userId
      name
      email
      location
      weddingDate
      category
      rating
      title
      review
      wouldRecommend
      verified
      status
      entityType
      entityId
      userEntityComposite
      serviceRating
      valueRating
      communicationRating
      professionalismRating
      images
      helpfulCount
      purchaseVerified
      reviewHelpfulUsers
      vendorResponse
      responseDate
      reviewTarget
      adminNotes
      moderatedBy
      moderatedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createContact = /* GraphQL */ `
  mutation CreateContact(
    $input: CreateContactInput!
    $condition: ModelContactConditionInput
  ) {
    createContact(input: $input, condition: $condition) {
      id
      name
      email
      phone
      subject
      message
      inquiryType
      status
      priority
      assignedTo
      responseMessage
      respondedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateContact = /* GraphQL */ `
  mutation UpdateContact(
    $input: UpdateContactInput!
    $condition: ModelContactConditionInput
  ) {
    updateContact(input: $input, condition: $condition) {
      id
      name
      email
      phone
      subject
      message
      inquiryType
      status
      priority
      assignedTo
      responseMessage
      respondedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteContact = /* GraphQL */ `
  mutation DeleteContact(
    $input: DeleteContactInput!
    $condition: ModelContactConditionInput
  ) {
    deleteContact(input: $input, condition: $condition) {
      id
      name
      email
      phone
      subject
      message
      inquiryType
      status
      priority
      assignedTo
      responseMessage
      respondedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createInquiry = /* GraphQL */ `
  mutation CreateInquiry(
    $input: CreateInquiryInput!
    $condition: ModelInquiryConditionInput
  ) {
    createInquiry(input: $input, condition: $condition) {
      id
      vendorUserId
      vendorId
      vendorName
      customerUserId
      customerName
      customerEmail
      customerPhone
      eventDate
      message
      inquiryType
      status
      priority
      budget
      guestCount
      venue
      additionalServices
      preferredContactTime
      responseMessage
      respondedAt
      assignedTo
      followUpDate
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateInquiry = /* GraphQL */ `
  mutation UpdateInquiry(
    $input: UpdateInquiryInput!
    $condition: ModelInquiryConditionInput
  ) {
    updateInquiry(input: $input, condition: $condition) {
      id
      vendorUserId
      vendorId
      vendorName
      customerUserId
      customerName
      customerEmail
      customerPhone
      eventDate
      message
      inquiryType
      status
      priority
      budget
      guestCount
      venue
      additionalServices
      preferredContactTime
      responseMessage
      respondedAt
      assignedTo
      followUpDate
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteInquiry = /* GraphQL */ `
  mutation DeleteInquiry(
    $input: DeleteInquiryInput!
    $condition: ModelInquiryConditionInput
  ) {
    deleteInquiry(input: $input, condition: $condition) {
      id
      vendorUserId
      vendorId
      vendorName
      customerUserId
      customerName
      customerEmail
      customerPhone
      eventDate
      message
      inquiryType
      status
      priority
      budget
      guestCount
      venue
      additionalServices
      preferredContactTime
      responseMessage
      respondedAt
      assignedTo
      followUpDate
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createUserProfile = /* GraphQL */ `
  mutation CreateUserProfile(
    $input: CreateUserProfileInput!
    $condition: ModelUserProfileConditionInput
  ) {
    createUserProfile(input: $input, condition: $condition) {
      id
      userId
      firstName
      lastName
      email
      phone
      dateOfBirth
      gender
      address
      city
      state
      pincode
      country
      profilePhoto
      bio
      website
      socialMedia {
        facebook
        instagram
        twitter
        linkedin
        youtube
        __typename
      }
      preferences {
        language
        currency
        timezone
        __typename
      }
      businessInfo {
        businessName
        businessType
        businessAddress
        businessPhone
        businessEmail
        businessWebsite
        gstNumber
        panNumber
        businessLicense
        __typename
      }
      isVendor
      isAdmin
      isSuperAdmin
      role
      permissions
      registrationSource
      accountType
      userTypeForm {
        formType
        submissionDate
        formVersion
        verificationStatus
        approvalStatus
        submittedBy
        reviewedBy
        reviewDate
        reviewNotes
        __typename
      }
      isVerified
      lastLoginAt
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const updateUserProfile = /* GraphQL */ `
  mutation UpdateUserProfile(
    $input: UpdateUserProfileInput!
    $condition: ModelUserProfileConditionInput
  ) {
    updateUserProfile(input: $input, condition: $condition) {
      id
      userId
      firstName
      lastName
      email
      phone
      dateOfBirth
      gender
      address
      city
      state
      pincode
      country
      profilePhoto
      bio
      website
      socialMedia {
        facebook
        instagram
        twitter
        linkedin
        youtube
        __typename
      }
      preferences {
        language
        currency
        timezone
        __typename
      }
      businessInfo {
        businessName
        businessType
        businessAddress
        businessPhone
        businessEmail
        businessWebsite
        gstNumber
        panNumber
        businessLicense
        __typename
      }
      isVendor
      isAdmin
      isSuperAdmin
      role
      permissions
      registrationSource
      accountType
      userTypeForm {
        formType
        submissionDate
        formVersion
        verificationStatus
        approvalStatus
        submittedBy
        reviewedBy
        reviewDate
        reviewNotes
        __typename
      }
      isVerified
      lastLoginAt
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const deleteUserProfile = /* GraphQL */ `
  mutation DeleteUserProfile(
    $input: DeleteUserProfileInput!
    $condition: ModelUserProfileConditionInput
  ) {
    deleteUserProfile(input: $input, condition: $condition) {
      id
      userId
      firstName
      lastName
      email
      phone
      dateOfBirth
      gender
      address
      city
      state
      pincode
      country
      profilePhoto
      bio
      website
      socialMedia {
        facebook
        instagram
        twitter
        linkedin
        youtube
        __typename
      }
      preferences {
        language
        currency
        timezone
        __typename
      }
      businessInfo {
        businessName
        businessType
        businessAddress
        businessPhone
        businessEmail
        businessWebsite
        gstNumber
        panNumber
        businessLicense
        __typename
      }
      isVendor
      isAdmin
      isSuperAdmin
      role
      permissions
      registrationSource
      accountType
      userTypeForm {
        formType
        submissionDate
        formVersion
        verificationStatus
        approvalStatus
        submittedBy
        reviewedBy
        reviewDate
        reviewNotes
        __typename
      }
      isVerified
      lastLoginAt
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const createBlog = /* GraphQL */ `
  mutation CreateBlog(
    $input: CreateBlogInput!
    $condition: ModelBlogConditionInput
  ) {
    createBlog(input: $input, condition: $condition) {
      id
      title
      content
      excerpt
      category
      authorId
      authorName
      authorType
      featuredImage
      tags
      status
      views
      likes
      comments
      isPinned
      isFeatured
      publishedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateBlog = /* GraphQL */ `
  mutation UpdateBlog(
    $input: UpdateBlogInput!
    $condition: ModelBlogConditionInput
  ) {
    updateBlog(input: $input, condition: $condition) {
      id
      title
      content
      excerpt
      category
      authorId
      authorName
      authorType
      featuredImage
      tags
      status
      views
      likes
      comments
      isPinned
      isFeatured
      publishedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteBlog = /* GraphQL */ `
  mutation DeleteBlog(
    $input: DeleteBlogInput!
    $condition: ModelBlogConditionInput
  ) {
    deleteBlog(input: $input, condition: $condition) {
      id
      title
      content
      excerpt
      category
      authorId
      authorName
      authorType
      featuredImage
      tags
      status
      views
      likes
      comments
      isPinned
      isFeatured
      publishedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createBooking = /* GraphQL */ `
  mutation CreateBooking(
    $input: CreateBookingInput!
    $condition: ModelBookingConditionInput
  ) {
    createBooking(input: $input, condition: $condition) {
      id
      customerId
      customerName
      customerEmail
      customerPhone
      entityId
      entityType
      entityName
      vendorId
      eventDate
      eventTime
      guestCount
      eventType
      duration
      specialRequests
      budget
      contactPreference
      status
      priority
      notes
      vendorNotes
      estimatedCost
      finalCost
      advanceAmount
      balanceAmount
      paymentStatus
      paymentMethod
      transactionId
      contractSigned
      contractUrl
      cancellationReason
      cancellationDate
      refundAmount
      refundStatus
      followUpDate
      reminderSent
      customerRating
      customerReview
      vendorRating
      vendorReview
      communicationLog {
        timestamp
        type
        from
        to
        subject
        message
        attachments
        status
        __typename
      }
      attachments
      metadata
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const updateBooking = /* GraphQL */ `
  mutation UpdateBooking(
    $input: UpdateBookingInput!
    $condition: ModelBookingConditionInput
  ) {
    updateBooking(input: $input, condition: $condition) {
      id
      customerId
      customerName
      customerEmail
      customerPhone
      entityId
      entityType
      entityName
      vendorId
      eventDate
      eventTime
      guestCount
      eventType
      duration
      specialRequests
      budget
      contactPreference
      status
      priority
      notes
      vendorNotes
      estimatedCost
      finalCost
      advanceAmount
      balanceAmount
      paymentStatus
      paymentMethod
      transactionId
      contractSigned
      contractUrl
      cancellationReason
      cancellationDate
      refundAmount
      refundStatus
      followUpDate
      reminderSent
      customerRating
      customerReview
      vendorRating
      vendorReview
      communicationLog {
        timestamp
        type
        from
        to
        subject
        message
        attachments
        status
        __typename
      }
      attachments
      metadata
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const deleteBooking = /* GraphQL */ `
  mutation DeleteBooking(
    $input: DeleteBookingInput!
    $condition: ModelBookingConditionInput
  ) {
    deleteBooking(input: $input, condition: $condition) {
      id
      customerId
      customerName
      customerEmail
      customerPhone
      entityId
      entityType
      entityName
      vendorId
      eventDate
      eventTime
      guestCount
      eventType
      duration
      specialRequests
      budget
      contactPreference
      status
      priority
      notes
      vendorNotes
      estimatedCost
      finalCost
      advanceAmount
      balanceAmount
      paymentStatus
      paymentMethod
      transactionId
      contractSigned
      contractUrl
      cancellationReason
      cancellationDate
      refundAmount
      refundStatus
      followUpDate
      reminderSent
      customerRating
      customerReview
      vendorRating
      vendorReview
      communicationLog {
        timestamp
        type
        from
        to
        subject
        message
        attachments
        status
        __typename
      }
      attachments
      metadata
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const createNewsletterSubscription = /* GraphQL */ `
  mutation CreateNewsletterSubscription(
    $input: CreateNewsletterSubscriptionInput!
    $condition: ModelNewsletterSubscriptionConditionInput
  ) {
    createNewsletterSubscription(input: $input, condition: $condition) {
      id
      email
      firstName
      lastName
      phone
      city
      state
      weddingDate
      interests
      source
      status
      preferences {
        weddingTips
        vendorRecommendations
        specialOffers
        eventUpdates
        blogUpdates
        frequency
        __typename
      }
      userId
      subscribedAt
      unsubscribedAt
      lastEmailSent
      emailsSent
      emailsOpened
      emailsClicked
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateNewsletterSubscription = /* GraphQL */ `
  mutation UpdateNewsletterSubscription(
    $input: UpdateNewsletterSubscriptionInput!
    $condition: ModelNewsletterSubscriptionConditionInput
  ) {
    updateNewsletterSubscription(input: $input, condition: $condition) {
      id
      email
      firstName
      lastName
      phone
      city
      state
      weddingDate
      interests
      source
      status
      preferences {
        weddingTips
        vendorRecommendations
        specialOffers
        eventUpdates
        blogUpdates
        frequency
        __typename
      }
      userId
      subscribedAt
      unsubscribedAt
      lastEmailSent
      emailsSent
      emailsOpened
      emailsClicked
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteNewsletterSubscription = /* GraphQL */ `
  mutation DeleteNewsletterSubscription(
    $input: DeleteNewsletterSubscriptionInput!
    $condition: ModelNewsletterSubscriptionConditionInput
  ) {
    deleteNewsletterSubscription(input: $input, condition: $condition) {
      id
      email
      firstName
      lastName
      phone
      city
      state
      weddingDate
      interests
      source
      status
      preferences {
        weddingTips
        vendorRecommendations
        specialOffers
        eventUpdates
        blogUpdates
        frequency
        __typename
      }
      userId
      subscribedAt
      unsubscribedAt
      lastEmailSent
      emailsSent
      emailsOpened
      emailsClicked
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createChecklistItem = /* GraphQL */ `
  mutation CreateChecklistItem(
    $input: CreateChecklistItemInput!
    $condition: ModelChecklistItemConditionInput
  ) {
    createChecklistItem(input: $input, condition: $condition) {
      id
      userId
      categoryId
      text
      completed
      dueDate
      priority
      order
      isDefault
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateChecklistItem = /* GraphQL */ `
  mutation UpdateChecklistItem(
    $input: UpdateChecklistItemInput!
    $condition: ModelChecklistItemConditionInput
  ) {
    updateChecklistItem(input: $input, condition: $condition) {
      id
      userId
      categoryId
      text
      completed
      dueDate
      priority
      order
      isDefault
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteChecklistItem = /* GraphQL */ `
  mutation DeleteChecklistItem(
    $input: DeleteChecklistItemInput!
    $condition: ModelChecklistItemConditionInput
  ) {
    deleteChecklistItem(input: $input, condition: $condition) {
      id
      userId
      categoryId
      text
      completed
      dueDate
      priority
      order
      isDefault
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createChecklistCategory = /* GraphQL */ `
  mutation CreateChecklistCategory(
    $input: CreateChecklistCategoryInput!
    $condition: ModelChecklistCategoryConditionInput
  ) {
    createChecklistCategory(input: $input, condition: $condition) {
      id
      userId
      name
      icon
      expanded
      order
      isDefault
      items {
        nextToken
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateChecklistCategory = /* GraphQL */ `
  mutation UpdateChecklistCategory(
    $input: UpdateChecklistCategoryInput!
    $condition: ModelChecklistCategoryConditionInput
  ) {
    updateChecklistCategory(input: $input, condition: $condition) {
      id
      userId
      name
      icon
      expanded
      order
      isDefault
      items {
        nextToken
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteChecklistCategory = /* GraphQL */ `
  mutation DeleteChecklistCategory(
    $input: DeleteChecklistCategoryInput!
    $condition: ModelChecklistCategoryConditionInput
  ) {
    deleteChecklistCategory(input: $input, condition: $condition) {
      id
      userId
      name
      icon
      expanded
      order
      isDefault
      items {
        nextToken
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createFavorite = /* GraphQL */ `
  mutation CreateFavorite(
    $input: CreateFavoriteInput!
    $condition: ModelFavoriteConditionInput
  ) {
    createFavorite(input: $input, condition: $condition) {
      id
      userId
      entityId
      entityType
      entityName
      entityImage
      entityPrice
      entityLocation
      entityCity
      entityState
      entityRating
      entityReviewCount
      entityDescription
      dateAdded
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const updateFavorite = /* GraphQL */ `
  mutation UpdateFavorite(
    $input: UpdateFavoriteInput!
    $condition: ModelFavoriteConditionInput
  ) {
    updateFavorite(input: $input, condition: $condition) {
      id
      userId
      entityId
      entityType
      entityName
      entityImage
      entityPrice
      entityLocation
      entityCity
      entityState
      entityRating
      entityReviewCount
      entityDescription
      dateAdded
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const deleteFavorite = /* GraphQL */ `
  mutation DeleteFavorite(
    $input: DeleteFavoriteInput!
    $condition: ModelFavoriteConditionInput
  ) {
    deleteFavorite(input: $input, condition: $condition) {
      id
      userId
      entityId
      entityType
      entityName
      entityImage
      entityPrice
      entityLocation
      entityCity
      entityState
      entityRating
      entityReviewCount
      entityDescription
      dateAdded
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const createCartItem = /* GraphQL */ `
  mutation CreateCartItem(
    $input: CreateCartItemInput!
    $condition: ModelCartItemConditionInput
  ) {
    createCartItem(input: $input, condition: $condition) {
      id
      userId
      productId
      productName
      productImage
      productPrice
      originalPrice
      discount
      quantity
      selectedVariant
      selectedSize
      selectedColor
      productBrand
      productCategory
      productDescription
      status
      dateAdded
      dateUpdated
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const updateCartItem = /* GraphQL */ `
  mutation UpdateCartItem(
    $input: UpdateCartItemInput!
    $condition: ModelCartItemConditionInput
  ) {
    updateCartItem(input: $input, condition: $condition) {
      id
      userId
      productId
      productName
      productImage
      productPrice
      originalPrice
      discount
      quantity
      selectedVariant
      selectedSize
      selectedColor
      productBrand
      productCategory
      productDescription
      status
      dateAdded
      dateUpdated
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const deleteCartItem = /* GraphQL */ `
  mutation DeleteCartItem(
    $input: DeleteCartItemInput!
    $condition: ModelCartItemConditionInput
  ) {
    deleteCartItem(input: $input, condition: $condition) {
      id
      userId
      productId
      productName
      productImage
      productPrice
      originalPrice
      discount
      quantity
      selectedVariant
      selectedSize
      selectedColor
      productBrand
      productCategory
      productDescription
      status
      dateAdded
      dateUpdated
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const createOrder = /* GraphQL */ `
  mutation CreateOrder(
    $input: CreateOrderInput!
    $condition: ModelOrderConditionInput
  ) {
    createOrder(input: $input, condition: $condition) {
      id
      userId
      orderNumber
      status
      paymentStatus
      paymentMethod
      customerName
      customerEmail
      customerPhone
      shippingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      billingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      items {
        productId
        productName
        productImage
        productPrice
        originalPrice
        discount
        quantity
        selectedVariant
        selectedSize
        selectedColor
        productBrand
        productCategory
        subtotal
        __typename
      }
      subtotal
      shippingCost
      tax
      discount
      total
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      transactionId
      estimatedDeliveryDate
      actualDeliveryDate
      trackingNumber
      courierPartner
      specialInstructions
      giftMessage
      isGift
      orderDate
      shippedDate
      deliveredDate
      cancelledDate
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const updateOrder = /* GraphQL */ `
  mutation UpdateOrder(
    $input: UpdateOrderInput!
    $condition: ModelOrderConditionInput
  ) {
    updateOrder(input: $input, condition: $condition) {
      id
      userId
      orderNumber
      status
      paymentStatus
      paymentMethod
      customerName
      customerEmail
      customerPhone
      shippingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      billingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      items {
        productId
        productName
        productImage
        productPrice
        originalPrice
        discount
        quantity
        selectedVariant
        selectedSize
        selectedColor
        productBrand
        productCategory
        subtotal
        __typename
      }
      subtotal
      shippingCost
      tax
      discount
      total
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      transactionId
      estimatedDeliveryDate
      actualDeliveryDate
      trackingNumber
      courierPartner
      specialInstructions
      giftMessage
      isGift
      orderDate
      shippedDate
      deliveredDate
      cancelledDate
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const deleteOrder = /* GraphQL */ `
  mutation DeleteOrder(
    $input: DeleteOrderInput!
    $condition: ModelOrderConditionInput
  ) {
    deleteOrder(input: $input, condition: $condition) {
      id
      userId
      orderNumber
      status
      paymentStatus
      paymentMethod
      customerName
      customerEmail
      customerPhone
      shippingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      billingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      items {
        productId
        productName
        productImage
        productPrice
        originalPrice
        discount
        quantity
        selectedVariant
        selectedSize
        selectedColor
        productBrand
        productCategory
        subtotal
        __typename
      }
      subtotal
      shippingCost
      tax
      discount
      total
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      transactionId
      estimatedDeliveryDate
      actualDeliveryDate
      trackingNumber
      courierPartner
      specialInstructions
      giftMessage
      isGift
      orderDate
      shippedDate
      deliveredDate
      cancelledDate
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const createPayment = /* GraphQL */ `
  mutation CreatePayment(
    $input: CreatePaymentInput!
    $condition: ModelPaymentConditionInput
  ) {
    createPayment(input: $input, condition: $condition) {
      id
      orderId
      userId
      amount
      currency
      paymentMethod
      status
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      codAmount
      codCollected
      codCollectedDate
      transactionId
      gatewayResponse
      failureReason
      refundAmount
      refundStatus
      refundDate
      refundTransactionId
      initiatedAt
      completedAt
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const updatePayment = /* GraphQL */ `
  mutation UpdatePayment(
    $input: UpdatePaymentInput!
    $condition: ModelPaymentConditionInput
  ) {
    updatePayment(input: $input, condition: $condition) {
      id
      orderId
      userId
      amount
      currency
      paymentMethod
      status
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      codAmount
      codCollected
      codCollectedDate
      transactionId
      gatewayResponse
      failureReason
      refundAmount
      refundStatus
      refundDate
      refundTransactionId
      initiatedAt
      completedAt
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const deletePayment = /* GraphQL */ `
  mutation DeletePayment(
    $input: DeletePaymentInput!
    $condition: ModelPaymentConditionInput
  ) {
    deletePayment(input: $input, condition: $condition) {
      id
      orderId
      userId
      amount
      currency
      paymentMethod
      status
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      codAmount
      codCollected
      codCollectedDate
      transactionId
      gatewayResponse
      failureReason
      refundAmount
      refundStatus
      refundDate
      refundTransactionId
      initiatedAt
      completedAt
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const createWeddingPlan = /* GraphQL */ `
  mutation CreateWeddingPlan(
    $input: CreateWeddingPlanInput!
    $condition: ModelWeddingPlanConditionInput
  ) {
    createWeddingPlan(input: $input, condition: $condition) {
      id
      userId
      weddingDate
      venue
      budget
      guestCount
      theme
      status
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateWeddingPlan = /* GraphQL */ `
  mutation UpdateWeddingPlan(
    $input: UpdateWeddingPlanInput!
    $condition: ModelWeddingPlanConditionInput
  ) {
    updateWeddingPlan(input: $input, condition: $condition) {
      id
      userId
      weddingDate
      venue
      budget
      guestCount
      theme
      status
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteWeddingPlan = /* GraphQL */ `
  mutation DeleteWeddingPlan(
    $input: DeleteWeddingPlanInput!
    $condition: ModelWeddingPlanConditionInput
  ) {
    deleteWeddingPlan(input: $input, condition: $condition) {
      id
      userId
      weddingDate
      venue
      budget
      guestCount
      theme
      status
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createBudget = /* GraphQL */ `
  mutation CreateBudget(
    $input: CreateBudgetInput!
    $condition: ModelBudgetConditionInput
  ) {
    createBudget(input: $input, condition: $condition) {
      id
      userId
      weddingPlanId
      name
      totalBudget
      spentAmount
      isTemplate
      templateType
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateBudget = /* GraphQL */ `
  mutation UpdateBudget(
    $input: UpdateBudgetInput!
    $condition: ModelBudgetConditionInput
  ) {
    updateBudget(input: $input, condition: $condition) {
      id
      userId
      weddingPlanId
      name
      totalBudget
      spentAmount
      isTemplate
      templateType
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteBudget = /* GraphQL */ `
  mutation DeleteBudget(
    $input: DeleteBudgetInput!
    $condition: ModelBudgetConditionInput
  ) {
    deleteBudget(input: $input, condition: $condition) {
      id
      userId
      weddingPlanId
      name
      totalBudget
      spentAmount
      isTemplate
      templateType
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createBudgetData = /* GraphQL */ `
  mutation CreateBudgetData(
    $input: CreateBudgetDataInput!
    $condition: ModelBudgetDataConditionInput
  ) {
    createBudgetData(input: $input, condition: $condition) {
      id
      userId
      budgetId
      name
      data
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateBudgetData = /* GraphQL */ `
  mutation UpdateBudgetData(
    $input: UpdateBudgetDataInput!
    $condition: ModelBudgetDataConditionInput
  ) {
    updateBudgetData(input: $input, condition: $condition) {
      id
      userId
      budgetId
      name
      data
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteBudgetData = /* GraphQL */ `
  mutation DeleteBudgetData(
    $input: DeleteBudgetDataInput!
    $condition: ModelBudgetDataConditionInput
  ) {
    deleteBudgetData(input: $input, condition: $condition) {
      id
      userId
      budgetId
      name
      data
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createGuestListData = /* GraphQL */ `
  mutation CreateGuestListData(
    $input: CreateGuestListDataInput!
    $condition: ModelGuestListDataConditionInput
  ) {
    createGuestListData(input: $input, condition: $condition) {
      id
      userId
      name
      data
      totalGuests
      confirmedGuests
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateGuestListData = /* GraphQL */ `
  mutation UpdateGuestListData(
    $input: UpdateGuestListDataInput!
    $condition: ModelGuestListDataConditionInput
  ) {
    updateGuestListData(input: $input, condition: $condition) {
      id
      userId
      name
      data
      totalGuests
      confirmedGuests
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteGuestListData = /* GraphQL */ `
  mutation DeleteGuestListData(
    $input: DeleteGuestListDataInput!
    $condition: ModelGuestListDataConditionInput
  ) {
    deleteGuestListData(input: $input, condition: $condition) {
      id
      userId
      name
      data
      totalGuests
      confirmedGuests
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const createPlanningToolsData = /* GraphQL */ `
  mutation CreatePlanningToolsData(
    $input: CreatePlanningToolsDataInput!
    $condition: ModelPlanningToolsDataConditionInput
  ) {
    createPlanningToolsData(input: $input, condition: $condition) {
      id
      userId
      toolType
      name
      data
      metadata
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updatePlanningToolsData = /* GraphQL */ `
  mutation UpdatePlanningToolsData(
    $input: UpdatePlanningToolsDataInput!
    $condition: ModelPlanningToolsDataConditionInput
  ) {
    updatePlanningToolsData(input: $input, condition: $condition) {
      id
      userId
      toolType
      name
      data
      metadata
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deletePlanningToolsData = /* GraphQL */ `
  mutation DeletePlanningToolsData(
    $input: DeletePlanningToolsDataInput!
    $condition: ModelPlanningToolsDataConditionInput
  ) {
    deletePlanningToolsData(input: $input, condition: $condition) {
      id
      userId
      toolType
      name
      data
      metadata
      createdAt
      updatedAt
      __typename
    }
  }
`;
