/* eslint-disable */
// this is an auto generated file. This will be overwritten

export const onCreateVendor = /* GraphQL */ `
  subscription OnCreateVendor($filter: ModelSubscriptionVendorFilterInput) {
    onCreateVendor(filter: $filter) {
      id
      userId
      name
      description
      contact
      email
      address
      city
      state
      pincode
      website
      category
      profilePhoto
      gallery
      services {
        name
        price
        description
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      experience
      events
      responseTime
      rating
      reviewCount
      verified
      featured
      availability
      priceRange
      specializations
      awards
      languages
      coverage
      equipment
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateVendor = /* GraphQL */ `
  subscription OnUpdateVendor($filter: ModelSubscriptionVendorFilterInput) {
    onUpdateVendor(filter: $filter) {
      id
      userId
      name
      description
      contact
      email
      address
      city
      state
      pincode
      website
      category
      profilePhoto
      gallery
      services {
        name
        price
        description
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      experience
      events
      responseTime
      rating
      reviewCount
      verified
      featured
      availability
      priceRange
      specializations
      awards
      languages
      coverage
      equipment
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteVendor = /* GraphQL */ `
  subscription OnDeleteVendor($filter: ModelSubscriptionVendorFilterInput) {
    onDeleteVendor(filter: $filter) {
      id
      userId
      name
      description
      contact
      email
      address
      city
      state
      pincode
      website
      category
      profilePhoto
      gallery
      services {
        name
        price
        description
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      experience
      events
      responseTime
      rating
      reviewCount
      verified
      featured
      availability
      priceRange
      specializations
      awards
      languages
      coverage
      equipment
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateVenue = /* GraphQL */ `
  subscription OnCreateVenue($filter: ModelSubscriptionVenueFilterInput) {
    onCreateVenue(filter: $filter) {
      id
      userId
      name
      description
      type
      capacity
      location
      city
      state
      fullAddress
      pincode
      contactPhone
      contactEmail
      website
      price
      priceRange
      images
      amenities
      spaces {
        name
        capacity
        area
        price
        description
        amenities
        images
        __typename
      }
      packages {
        name
        price
        duration
        description
        includes
        excludes
        terms
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      rating
      reviewCount
      bookings
      verified
      featured
      status
      availability
      policies {
        cancellation
        advance
        catering
        decoration
        alcohol
        music
        parking
        __typename
      }
      coordinates {
        latitude
        longitude
        __typename
      }
      operatingHours {
        monday
        tuesday
        wednesday
        thursday
        friday
        saturday
        sunday
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateVenue = /* GraphQL */ `
  subscription OnUpdateVenue($filter: ModelSubscriptionVenueFilterInput) {
    onUpdateVenue(filter: $filter) {
      id
      userId
      name
      description
      type
      capacity
      location
      city
      state
      fullAddress
      pincode
      contactPhone
      contactEmail
      website
      price
      priceRange
      images
      amenities
      spaces {
        name
        capacity
        area
        price
        description
        amenities
        images
        __typename
      }
      packages {
        name
        price
        duration
        description
        includes
        excludes
        terms
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      rating
      reviewCount
      bookings
      verified
      featured
      status
      availability
      policies {
        cancellation
        advance
        catering
        decoration
        alcohol
        music
        parking
        __typename
      }
      coordinates {
        latitude
        longitude
        __typename
      }
      operatingHours {
        monday
        tuesday
        wednesday
        thursday
        friday
        saturday
        sunday
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteVenue = /* GraphQL */ `
  subscription OnDeleteVenue($filter: ModelSubscriptionVenueFilterInput) {
    onDeleteVenue(filter: $filter) {
      id
      userId
      name
      description
      type
      capacity
      location
      city
      state
      fullAddress
      pincode
      contactPhone
      contactEmail
      website
      price
      priceRange
      images
      amenities
      spaces {
        name
        capacity
        area
        price
        description
        amenities
        images
        __typename
      }
      packages {
        name
        price
        duration
        description
        includes
        excludes
        terms
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      rating
      reviewCount
      bookings
      verified
      featured
      status
      availability
      policies {
        cancellation
        advance
        catering
        decoration
        alcohol
        music
        parking
        __typename
      }
      coordinates {
        latitude
        longitude
        __typename
      }
      operatingHours {
        monday
        tuesday
        wednesday
        thursday
        friday
        saturday
        sunday
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateShop = /* GraphQL */ `
  subscription OnCreateShop(
    $filter: ModelSubscriptionShopFilterInput
    $userId: String
  ) {
    onCreateShop(filter: $filter, userId: $userId) {
      id
      userId
      name
      category
      price
      originalPrice
      discount
      stock
      sku
      brand
      featured
      description
      features
      sizes
      colors
      images
      specifications {
        fabric
        work
        occasion
        care
        delivery
        returnPolicy
        __typename
      }
      rating
      reviewCount
      inStock
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateShop = /* GraphQL */ `
  subscription OnUpdateShop(
    $filter: ModelSubscriptionShopFilterInput
    $userId: String
  ) {
    onUpdateShop(filter: $filter, userId: $userId) {
      id
      userId
      name
      category
      price
      originalPrice
      discount
      stock
      sku
      brand
      featured
      description
      features
      sizes
      colors
      images
      specifications {
        fabric
        work
        occasion
        care
        delivery
        returnPolicy
        __typename
      }
      rating
      reviewCount
      inStock
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteShop = /* GraphQL */ `
  subscription OnDeleteShop(
    $filter: ModelSubscriptionShopFilterInput
    $userId: String
  ) {
    onDeleteShop(filter: $filter, userId: $userId) {
      id
      userId
      name
      category
      price
      originalPrice
      discount
      stock
      sku
      brand
      featured
      description
      features
      sizes
      colors
      images
      specifications {
        fabric
        work
        occasion
        care
        delivery
        returnPolicy
        __typename
      }
      rating
      reviewCount
      inStock
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateReview = /* GraphQL */ `
  subscription OnCreateReview(
    $filter: ModelSubscriptionReviewFilterInput
    $userId: String
  ) {
    onCreateReview(filter: $filter, userId: $userId) {
      id
      userId
      name
      email
      location
      weddingDate
      category
      rating
      title
      review
      wouldRecommend
      verified
      status
      entityType
      entityId
      userEntityComposite
      serviceRating
      valueRating
      communicationRating
      professionalismRating
      images
      helpfulCount
      purchaseVerified
      reviewHelpfulUsers
      vendorResponse
      responseDate
      reviewTarget
      adminNotes
      moderatedBy
      moderatedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateReview = /* GraphQL */ `
  subscription OnUpdateReview(
    $filter: ModelSubscriptionReviewFilterInput
    $userId: String
  ) {
    onUpdateReview(filter: $filter, userId: $userId) {
      id
      userId
      name
      email
      location
      weddingDate
      category
      rating
      title
      review
      wouldRecommend
      verified
      status
      entityType
      entityId
      userEntityComposite
      serviceRating
      valueRating
      communicationRating
      professionalismRating
      images
      helpfulCount
      purchaseVerified
      reviewHelpfulUsers
      vendorResponse
      responseDate
      reviewTarget
      adminNotes
      moderatedBy
      moderatedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteReview = /* GraphQL */ `
  subscription OnDeleteReview(
    $filter: ModelSubscriptionReviewFilterInput
    $userId: String
  ) {
    onDeleteReview(filter: $filter, userId: $userId) {
      id
      userId
      name
      email
      location
      weddingDate
      category
      rating
      title
      review
      wouldRecommend
      verified
      status
      entityType
      entityId
      userEntityComposite
      serviceRating
      valueRating
      communicationRating
      professionalismRating
      images
      helpfulCount
      purchaseVerified
      reviewHelpfulUsers
      vendorResponse
      responseDate
      reviewTarget
      adminNotes
      moderatedBy
      moderatedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateContact = /* GraphQL */ `
  subscription OnCreateContact($filter: ModelSubscriptionContactFilterInput) {
    onCreateContact(filter: $filter) {
      id
      name
      email
      phone
      subject
      message
      inquiryType
      status
      priority
      assignedTo
      responseMessage
      respondedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateContact = /* GraphQL */ `
  subscription OnUpdateContact($filter: ModelSubscriptionContactFilterInput) {
    onUpdateContact(filter: $filter) {
      id
      name
      email
      phone
      subject
      message
      inquiryType
      status
      priority
      assignedTo
      responseMessage
      respondedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteContact = /* GraphQL */ `
  subscription OnDeleteContact($filter: ModelSubscriptionContactFilterInput) {
    onDeleteContact(filter: $filter) {
      id
      name
      email
      phone
      subject
      message
      inquiryType
      status
      priority
      assignedTo
      responseMessage
      respondedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateInquiry = /* GraphQL */ `
  subscription OnCreateInquiry(
    $filter: ModelSubscriptionInquiryFilterInput
    $customerUserId: String
    $vendorUserId: String
  ) {
    onCreateInquiry(
      filter: $filter
      customerUserId: $customerUserId
      vendorUserId: $vendorUserId
    ) {
      id
      vendorUserId
      vendorId
      vendorName
      customerUserId
      customerName
      customerEmail
      customerPhone
      eventDate
      message
      inquiryType
      status
      priority
      budget
      guestCount
      venue
      additionalServices
      preferredContactTime
      responseMessage
      respondedAt
      assignedTo
      followUpDate
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateInquiry = /* GraphQL */ `
  subscription OnUpdateInquiry(
    $filter: ModelSubscriptionInquiryFilterInput
    $customerUserId: String
    $vendorUserId: String
  ) {
    onUpdateInquiry(
      filter: $filter
      customerUserId: $customerUserId
      vendorUserId: $vendorUserId
    ) {
      id
      vendorUserId
      vendorId
      vendorName
      customerUserId
      customerName
      customerEmail
      customerPhone
      eventDate
      message
      inquiryType
      status
      priority
      budget
      guestCount
      venue
      additionalServices
      preferredContactTime
      responseMessage
      respondedAt
      assignedTo
      followUpDate
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteInquiry = /* GraphQL */ `
  subscription OnDeleteInquiry(
    $filter: ModelSubscriptionInquiryFilterInput
    $customerUserId: String
    $vendorUserId: String
  ) {
    onDeleteInquiry(
      filter: $filter
      customerUserId: $customerUserId
      vendorUserId: $vendorUserId
    ) {
      id
      vendorUserId
      vendorId
      vendorName
      customerUserId
      customerName
      customerEmail
      customerPhone
      eventDate
      message
      inquiryType
      status
      priority
      budget
      guestCount
      venue
      additionalServices
      preferredContactTime
      responseMessage
      respondedAt
      assignedTo
      followUpDate
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateUserProfile = /* GraphQL */ `
  subscription OnCreateUserProfile(
    $filter: ModelSubscriptionUserProfileFilterInput
    $owner: String
  ) {
    onCreateUserProfile(filter: $filter, owner: $owner) {
      id
      userId
      firstName
      lastName
      email
      phone
      dateOfBirth
      gender
      address
      city
      state
      pincode
      country
      profilePhoto
      bio
      website
      socialMedia {
        facebook
        instagram
        twitter
        linkedin
        youtube
        __typename
      }
      preferences {
        language
        currency
        timezone
        __typename
      }
      businessInfo {
        businessName
        businessType
        businessAddress
        businessPhone
        businessEmail
        businessWebsite
        gstNumber
        panNumber
        businessLicense
        __typename
      }
      isVendor
      isAdmin
      isSuperAdmin
      role
      permissions
      registrationSource
      accountType
      userTypeForm {
        formType
        submissionDate
        formVersion
        verificationStatus
        approvalStatus
        submittedBy
        reviewedBy
        reviewDate
        reviewNotes
        __typename
      }
      isVerified
      lastLoginAt
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onUpdateUserProfile = /* GraphQL */ `
  subscription OnUpdateUserProfile(
    $filter: ModelSubscriptionUserProfileFilterInput
    $owner: String
  ) {
    onUpdateUserProfile(filter: $filter, owner: $owner) {
      id
      userId
      firstName
      lastName
      email
      phone
      dateOfBirth
      gender
      address
      city
      state
      pincode
      country
      profilePhoto
      bio
      website
      socialMedia {
        facebook
        instagram
        twitter
        linkedin
        youtube
        __typename
      }
      preferences {
        language
        currency
        timezone
        __typename
      }
      businessInfo {
        businessName
        businessType
        businessAddress
        businessPhone
        businessEmail
        businessWebsite
        gstNumber
        panNumber
        businessLicense
        __typename
      }
      isVendor
      isAdmin
      isSuperAdmin
      role
      permissions
      registrationSource
      accountType
      userTypeForm {
        formType
        submissionDate
        formVersion
        verificationStatus
        approvalStatus
        submittedBy
        reviewedBy
        reviewDate
        reviewNotes
        __typename
      }
      isVerified
      lastLoginAt
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onDeleteUserProfile = /* GraphQL */ `
  subscription OnDeleteUserProfile(
    $filter: ModelSubscriptionUserProfileFilterInput
    $owner: String
  ) {
    onDeleteUserProfile(filter: $filter, owner: $owner) {
      id
      userId
      firstName
      lastName
      email
      phone
      dateOfBirth
      gender
      address
      city
      state
      pincode
      country
      profilePhoto
      bio
      website
      socialMedia {
        facebook
        instagram
        twitter
        linkedin
        youtube
        __typename
      }
      preferences {
        language
        currency
        timezone
        __typename
      }
      businessInfo {
        businessName
        businessType
        businessAddress
        businessPhone
        businessEmail
        businessWebsite
        gstNumber
        panNumber
        businessLicense
        __typename
      }
      isVendor
      isAdmin
      isSuperAdmin
      role
      permissions
      registrationSource
      accountType
      userTypeForm {
        formType
        submissionDate
        formVersion
        verificationStatus
        approvalStatus
        submittedBy
        reviewedBy
        reviewDate
        reviewNotes
        __typename
      }
      isVerified
      lastLoginAt
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onCreateBlog = /* GraphQL */ `
  subscription OnCreateBlog(
    $filter: ModelSubscriptionBlogFilterInput
    $authorId: String
  ) {
    onCreateBlog(filter: $filter, authorId: $authorId) {
      id
      title
      content
      excerpt
      category
      authorId
      authorName
      authorType
      featuredImage
      tags
      status
      views
      likes
      comments
      isPinned
      isFeatured
      publishedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateBlog = /* GraphQL */ `
  subscription OnUpdateBlog(
    $filter: ModelSubscriptionBlogFilterInput
    $authorId: String
  ) {
    onUpdateBlog(filter: $filter, authorId: $authorId) {
      id
      title
      content
      excerpt
      category
      authorId
      authorName
      authorType
      featuredImage
      tags
      status
      views
      likes
      comments
      isPinned
      isFeatured
      publishedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteBlog = /* GraphQL */ `
  subscription OnDeleteBlog(
    $filter: ModelSubscriptionBlogFilterInput
    $authorId: String
  ) {
    onDeleteBlog(filter: $filter, authorId: $authorId) {
      id
      title
      content
      excerpt
      category
      authorId
      authorName
      authorType
      featuredImage
      tags
      status
      views
      likes
      comments
      isPinned
      isFeatured
      publishedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateBooking = /* GraphQL */ `
  subscription OnCreateBooking(
    $filter: ModelSubscriptionBookingFilterInput
    $owner: String
  ) {
    onCreateBooking(filter: $filter, owner: $owner) {
      id
      customerId
      customerName
      customerEmail
      customerPhone
      entityId
      entityType
      entityName
      vendorId
      eventDate
      eventTime
      guestCount
      eventType
      duration
      specialRequests
      budget
      contactPreference
      status
      priority
      notes
      vendorNotes
      estimatedCost
      finalCost
      advanceAmount
      balanceAmount
      paymentStatus
      paymentMethod
      transactionId
      contractSigned
      contractUrl
      cancellationReason
      cancellationDate
      refundAmount
      refundStatus
      followUpDate
      reminderSent
      customerRating
      customerReview
      vendorRating
      vendorReview
      communicationLog {
        timestamp
        type
        from
        to
        subject
        message
        attachments
        status
        __typename
      }
      attachments
      metadata
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onUpdateBooking = /* GraphQL */ `
  subscription OnUpdateBooking(
    $filter: ModelSubscriptionBookingFilterInput
    $owner: String
  ) {
    onUpdateBooking(filter: $filter, owner: $owner) {
      id
      customerId
      customerName
      customerEmail
      customerPhone
      entityId
      entityType
      entityName
      vendorId
      eventDate
      eventTime
      guestCount
      eventType
      duration
      specialRequests
      budget
      contactPreference
      status
      priority
      notes
      vendorNotes
      estimatedCost
      finalCost
      advanceAmount
      balanceAmount
      paymentStatus
      paymentMethod
      transactionId
      contractSigned
      contractUrl
      cancellationReason
      cancellationDate
      refundAmount
      refundStatus
      followUpDate
      reminderSent
      customerRating
      customerReview
      vendorRating
      vendorReview
      communicationLog {
        timestamp
        type
        from
        to
        subject
        message
        attachments
        status
        __typename
      }
      attachments
      metadata
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onDeleteBooking = /* GraphQL */ `
  subscription OnDeleteBooking(
    $filter: ModelSubscriptionBookingFilterInput
    $owner: String
  ) {
    onDeleteBooking(filter: $filter, owner: $owner) {
      id
      customerId
      customerName
      customerEmail
      customerPhone
      entityId
      entityType
      entityName
      vendorId
      eventDate
      eventTime
      guestCount
      eventType
      duration
      specialRequests
      budget
      contactPreference
      status
      priority
      notes
      vendorNotes
      estimatedCost
      finalCost
      advanceAmount
      balanceAmount
      paymentStatus
      paymentMethod
      transactionId
      contractSigned
      contractUrl
      cancellationReason
      cancellationDate
      refundAmount
      refundStatus
      followUpDate
      reminderSent
      customerRating
      customerReview
      vendorRating
      vendorReview
      communicationLog {
        timestamp
        type
        from
        to
        subject
        message
        attachments
        status
        __typename
      }
      attachments
      metadata
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onCreateNewsletterSubscription = /* GraphQL */ `
  subscription OnCreateNewsletterSubscription(
    $filter: ModelSubscriptionNewsletterSubscriptionFilterInput
  ) {
    onCreateNewsletterSubscription(filter: $filter) {
      id
      email
      firstName
      lastName
      phone
      city
      state
      weddingDate
      interests
      source
      status
      preferences {
        weddingTips
        vendorRecommendations
        specialOffers
        eventUpdates
        blogUpdates
        frequency
        __typename
      }
      userId
      subscribedAt
      unsubscribedAt
      lastEmailSent
      emailsSent
      emailsOpened
      emailsClicked
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateNewsletterSubscription = /* GraphQL */ `
  subscription OnUpdateNewsletterSubscription(
    $filter: ModelSubscriptionNewsletterSubscriptionFilterInput
  ) {
    onUpdateNewsletterSubscription(filter: $filter) {
      id
      email
      firstName
      lastName
      phone
      city
      state
      weddingDate
      interests
      source
      status
      preferences {
        weddingTips
        vendorRecommendations
        specialOffers
        eventUpdates
        blogUpdates
        frequency
        __typename
      }
      userId
      subscribedAt
      unsubscribedAt
      lastEmailSent
      emailsSent
      emailsOpened
      emailsClicked
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteNewsletterSubscription = /* GraphQL */ `
  subscription OnDeleteNewsletterSubscription(
    $filter: ModelSubscriptionNewsletterSubscriptionFilterInput
  ) {
    onDeleteNewsletterSubscription(filter: $filter) {
      id
      email
      firstName
      lastName
      phone
      city
      state
      weddingDate
      interests
      source
      status
      preferences {
        weddingTips
        vendorRecommendations
        specialOffers
        eventUpdates
        blogUpdates
        frequency
        __typename
      }
      userId
      subscribedAt
      unsubscribedAt
      lastEmailSent
      emailsSent
      emailsOpened
      emailsClicked
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateChecklistItem = /* GraphQL */ `
  subscription OnCreateChecklistItem(
    $filter: ModelSubscriptionChecklistItemFilterInput
    $userId: String
  ) {
    onCreateChecklistItem(filter: $filter, userId: $userId) {
      id
      userId
      categoryId
      text
      completed
      dueDate
      priority
      order
      isDefault
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateChecklistItem = /* GraphQL */ `
  subscription OnUpdateChecklistItem(
    $filter: ModelSubscriptionChecklistItemFilterInput
    $userId: String
  ) {
    onUpdateChecklistItem(filter: $filter, userId: $userId) {
      id
      userId
      categoryId
      text
      completed
      dueDate
      priority
      order
      isDefault
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteChecklistItem = /* GraphQL */ `
  subscription OnDeleteChecklistItem(
    $filter: ModelSubscriptionChecklistItemFilterInput
    $userId: String
  ) {
    onDeleteChecklistItem(filter: $filter, userId: $userId) {
      id
      userId
      categoryId
      text
      completed
      dueDate
      priority
      order
      isDefault
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateChecklistCategory = /* GraphQL */ `
  subscription OnCreateChecklistCategory(
    $filter: ModelSubscriptionChecklistCategoryFilterInput
    $userId: String
  ) {
    onCreateChecklistCategory(filter: $filter, userId: $userId) {
      id
      userId
      name
      icon
      expanded
      order
      isDefault
      items {
        nextToken
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateChecklistCategory = /* GraphQL */ `
  subscription OnUpdateChecklistCategory(
    $filter: ModelSubscriptionChecklistCategoryFilterInput
    $userId: String
  ) {
    onUpdateChecklistCategory(filter: $filter, userId: $userId) {
      id
      userId
      name
      icon
      expanded
      order
      isDefault
      items {
        nextToken
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteChecklistCategory = /* GraphQL */ `
  subscription OnDeleteChecklistCategory(
    $filter: ModelSubscriptionChecklistCategoryFilterInput
    $userId: String
  ) {
    onDeleteChecklistCategory(filter: $filter, userId: $userId) {
      id
      userId
      name
      icon
      expanded
      order
      isDefault
      items {
        nextToken
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateFavorite = /* GraphQL */ `
  subscription OnCreateFavorite(
    $filter: ModelSubscriptionFavoriteFilterInput
    $owner: String
  ) {
    onCreateFavorite(filter: $filter, owner: $owner) {
      id
      userId
      entityId
      entityType
      entityName
      entityImage
      entityPrice
      entityLocation
      entityCity
      entityState
      entityRating
      entityReviewCount
      entityDescription
      dateAdded
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onUpdateFavorite = /* GraphQL */ `
  subscription OnUpdateFavorite(
    $filter: ModelSubscriptionFavoriteFilterInput
    $owner: String
  ) {
    onUpdateFavorite(filter: $filter, owner: $owner) {
      id
      userId
      entityId
      entityType
      entityName
      entityImage
      entityPrice
      entityLocation
      entityCity
      entityState
      entityRating
      entityReviewCount
      entityDescription
      dateAdded
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onDeleteFavorite = /* GraphQL */ `
  subscription OnDeleteFavorite(
    $filter: ModelSubscriptionFavoriteFilterInput
    $owner: String
  ) {
    onDeleteFavorite(filter: $filter, owner: $owner) {
      id
      userId
      entityId
      entityType
      entityName
      entityImage
      entityPrice
      entityLocation
      entityCity
      entityState
      entityRating
      entityReviewCount
      entityDescription
      dateAdded
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onCreateCartItem = /* GraphQL */ `
  subscription OnCreateCartItem(
    $filter: ModelSubscriptionCartItemFilterInput
    $owner: String
  ) {
    onCreateCartItem(filter: $filter, owner: $owner) {
      id
      userId
      productId
      productName
      productImage
      productPrice
      originalPrice
      discount
      quantity
      selectedVariant
      selectedSize
      selectedColor
      productBrand
      productCategory
      productDescription
      status
      dateAdded
      dateUpdated
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onUpdateCartItem = /* GraphQL */ `
  subscription OnUpdateCartItem(
    $filter: ModelSubscriptionCartItemFilterInput
    $owner: String
  ) {
    onUpdateCartItem(filter: $filter, owner: $owner) {
      id
      userId
      productId
      productName
      productImage
      productPrice
      originalPrice
      discount
      quantity
      selectedVariant
      selectedSize
      selectedColor
      productBrand
      productCategory
      productDescription
      status
      dateAdded
      dateUpdated
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onDeleteCartItem = /* GraphQL */ `
  subscription OnDeleteCartItem(
    $filter: ModelSubscriptionCartItemFilterInput
    $owner: String
  ) {
    onDeleteCartItem(filter: $filter, owner: $owner) {
      id
      userId
      productId
      productName
      productImage
      productPrice
      originalPrice
      discount
      quantity
      selectedVariant
      selectedSize
      selectedColor
      productBrand
      productCategory
      productDescription
      status
      dateAdded
      dateUpdated
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onCreateOrder = /* GraphQL */ `
  subscription OnCreateOrder(
    $filter: ModelSubscriptionOrderFilterInput
    $owner: String
  ) {
    onCreateOrder(filter: $filter, owner: $owner) {
      id
      userId
      orderNumber
      status
      paymentStatus
      paymentMethod
      customerName
      customerEmail
      customerPhone
      shippingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      billingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      items {
        productId
        productName
        productImage
        productPrice
        originalPrice
        discount
        quantity
        selectedVariant
        selectedSize
        selectedColor
        productBrand
        productCategory
        subtotal
        __typename
      }
      subtotal
      shippingCost
      tax
      discount
      total
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      transactionId
      estimatedDeliveryDate
      actualDeliveryDate
      trackingNumber
      courierPartner
      specialInstructions
      giftMessage
      isGift
      orderDate
      shippedDate
      deliveredDate
      cancelledDate
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onUpdateOrder = /* GraphQL */ `
  subscription OnUpdateOrder(
    $filter: ModelSubscriptionOrderFilterInput
    $owner: String
  ) {
    onUpdateOrder(filter: $filter, owner: $owner) {
      id
      userId
      orderNumber
      status
      paymentStatus
      paymentMethod
      customerName
      customerEmail
      customerPhone
      shippingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      billingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      items {
        productId
        productName
        productImage
        productPrice
        originalPrice
        discount
        quantity
        selectedVariant
        selectedSize
        selectedColor
        productBrand
        productCategory
        subtotal
        __typename
      }
      subtotal
      shippingCost
      tax
      discount
      total
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      transactionId
      estimatedDeliveryDate
      actualDeliveryDate
      trackingNumber
      courierPartner
      specialInstructions
      giftMessage
      isGift
      orderDate
      shippedDate
      deliveredDate
      cancelledDate
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onDeleteOrder = /* GraphQL */ `
  subscription OnDeleteOrder(
    $filter: ModelSubscriptionOrderFilterInput
    $owner: String
  ) {
    onDeleteOrder(filter: $filter, owner: $owner) {
      id
      userId
      orderNumber
      status
      paymentStatus
      paymentMethod
      customerName
      customerEmail
      customerPhone
      shippingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      billingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      items {
        productId
        productName
        productImage
        productPrice
        originalPrice
        discount
        quantity
        selectedVariant
        selectedSize
        selectedColor
        productBrand
        productCategory
        subtotal
        __typename
      }
      subtotal
      shippingCost
      tax
      discount
      total
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      transactionId
      estimatedDeliveryDate
      actualDeliveryDate
      trackingNumber
      courierPartner
      specialInstructions
      giftMessage
      isGift
      orderDate
      shippedDate
      deliveredDate
      cancelledDate
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onCreatePayment = /* GraphQL */ `
  subscription OnCreatePayment(
    $filter: ModelSubscriptionPaymentFilterInput
    $owner: String
  ) {
    onCreatePayment(filter: $filter, owner: $owner) {
      id
      orderId
      userId
      amount
      currency
      paymentMethod
      status
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      codAmount
      codCollected
      codCollectedDate
      transactionId
      gatewayResponse
      failureReason
      refundAmount
      refundStatus
      refundDate
      refundTransactionId
      initiatedAt
      completedAt
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onUpdatePayment = /* GraphQL */ `
  subscription OnUpdatePayment(
    $filter: ModelSubscriptionPaymentFilterInput
    $owner: String
  ) {
    onUpdatePayment(filter: $filter, owner: $owner) {
      id
      orderId
      userId
      amount
      currency
      paymentMethod
      status
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      codAmount
      codCollected
      codCollectedDate
      transactionId
      gatewayResponse
      failureReason
      refundAmount
      refundStatus
      refundDate
      refundTransactionId
      initiatedAt
      completedAt
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onDeletePayment = /* GraphQL */ `
  subscription OnDeletePayment(
    $filter: ModelSubscriptionPaymentFilterInput
    $owner: String
  ) {
    onDeletePayment(filter: $filter, owner: $owner) {
      id
      orderId
      userId
      amount
      currency
      paymentMethod
      status
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      codAmount
      codCollected
      codCollectedDate
      transactionId
      gatewayResponse
      failureReason
      refundAmount
      refundStatus
      refundDate
      refundTransactionId
      initiatedAt
      completedAt
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const onCreateWeddingPlan = /* GraphQL */ `
  subscription OnCreateWeddingPlan(
    $filter: ModelSubscriptionWeddingPlanFilterInput
    $userId: String
  ) {
    onCreateWeddingPlan(filter: $filter, userId: $userId) {
      id
      userId
      weddingDate
      venue
      budget
      guestCount
      theme
      status
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateWeddingPlan = /* GraphQL */ `
  subscription OnUpdateWeddingPlan(
    $filter: ModelSubscriptionWeddingPlanFilterInput
    $userId: String
  ) {
    onUpdateWeddingPlan(filter: $filter, userId: $userId) {
      id
      userId
      weddingDate
      venue
      budget
      guestCount
      theme
      status
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteWeddingPlan = /* GraphQL */ `
  subscription OnDeleteWeddingPlan(
    $filter: ModelSubscriptionWeddingPlanFilterInput
    $userId: String
  ) {
    onDeleteWeddingPlan(filter: $filter, userId: $userId) {
      id
      userId
      weddingDate
      venue
      budget
      guestCount
      theme
      status
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateBudget = /* GraphQL */ `
  subscription OnCreateBudget(
    $filter: ModelSubscriptionBudgetFilterInput
    $userId: String
  ) {
    onCreateBudget(filter: $filter, userId: $userId) {
      id
      userId
      weddingPlanId
      name
      totalBudget
      spentAmount
      isTemplate
      templateType
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateBudget = /* GraphQL */ `
  subscription OnUpdateBudget(
    $filter: ModelSubscriptionBudgetFilterInput
    $userId: String
  ) {
    onUpdateBudget(filter: $filter, userId: $userId) {
      id
      userId
      weddingPlanId
      name
      totalBudget
      spentAmount
      isTemplate
      templateType
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteBudget = /* GraphQL */ `
  subscription OnDeleteBudget(
    $filter: ModelSubscriptionBudgetFilterInput
    $userId: String
  ) {
    onDeleteBudget(filter: $filter, userId: $userId) {
      id
      userId
      weddingPlanId
      name
      totalBudget
      spentAmount
      isTemplate
      templateType
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateBudgetData = /* GraphQL */ `
  subscription OnCreateBudgetData(
    $filter: ModelSubscriptionBudgetDataFilterInput
    $userId: String
  ) {
    onCreateBudgetData(filter: $filter, userId: $userId) {
      id
      userId
      budgetId
      name
      data
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateBudgetData = /* GraphQL */ `
  subscription OnUpdateBudgetData(
    $filter: ModelSubscriptionBudgetDataFilterInput
    $userId: String
  ) {
    onUpdateBudgetData(filter: $filter, userId: $userId) {
      id
      userId
      budgetId
      name
      data
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteBudgetData = /* GraphQL */ `
  subscription OnDeleteBudgetData(
    $filter: ModelSubscriptionBudgetDataFilterInput
    $userId: String
  ) {
    onDeleteBudgetData(filter: $filter, userId: $userId) {
      id
      userId
      budgetId
      name
      data
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreateGuestListData = /* GraphQL */ `
  subscription OnCreateGuestListData(
    $filter: ModelSubscriptionGuestListDataFilterInput
    $userId: String
  ) {
    onCreateGuestListData(filter: $filter, userId: $userId) {
      id
      userId
      name
      data
      totalGuests
      confirmedGuests
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdateGuestListData = /* GraphQL */ `
  subscription OnUpdateGuestListData(
    $filter: ModelSubscriptionGuestListDataFilterInput
    $userId: String
  ) {
    onUpdateGuestListData(filter: $filter, userId: $userId) {
      id
      userId
      name
      data
      totalGuests
      confirmedGuests
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeleteGuestListData = /* GraphQL */ `
  subscription OnDeleteGuestListData(
    $filter: ModelSubscriptionGuestListDataFilterInput
    $userId: String
  ) {
    onDeleteGuestListData(filter: $filter, userId: $userId) {
      id
      userId
      name
      data
      totalGuests
      confirmedGuests
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onCreatePlanningToolsData = /* GraphQL */ `
  subscription OnCreatePlanningToolsData(
    $filter: ModelSubscriptionPlanningToolsDataFilterInput
    $userId: String
  ) {
    onCreatePlanningToolsData(filter: $filter, userId: $userId) {
      id
      userId
      toolType
      name
      data
      metadata
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onUpdatePlanningToolsData = /* GraphQL */ `
  subscription OnUpdatePlanningToolsData(
    $filter: ModelSubscriptionPlanningToolsDataFilterInput
    $userId: String
  ) {
    onUpdatePlanningToolsData(filter: $filter, userId: $userId) {
      id
      userId
      toolType
      name
      data
      metadata
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const onDeletePlanningToolsData = /* GraphQL */ `
  subscription OnDeletePlanningToolsData(
    $filter: ModelSubscriptionPlanningToolsDataFilterInput
    $userId: String
  ) {
    onDeletePlanningToolsData(filter: $filter, userId: $userId) {
      id
      userId
      toolType
      name
      data
      metadata
      createdAt
      updatedAt
      __typename
    }
  }
`;
