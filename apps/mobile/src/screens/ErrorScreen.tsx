import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../providers/ThemeProvider';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';

export default function ErrorScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  
  // Get error message from route params
  const errorMessage = (route.params as any)?.message || 'An unexpected error occurred';

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 20,
      justifyContent: 'center',
    },
    content: {
      alignItems: 'center',
      gap: 24,
    },
    errorIcon: {
      fontSize: 64,
      color: theme.colors.destructive,
      marginBottom: 16,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: 8,
    },
    message: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
      marginBottom: 24,
    },
    buttonContainer: {
      width: '100%',
      gap: 12,
    },
  });

  return (
    <View style={styles.container}>
      <Card>
        <View style={styles.content}>
          <Text style={styles.errorIcon}>⚠️</Text>
          <Text style={styles.title}>Oops! Something went wrong</Text>
          <Text style={styles.message}>{errorMessage}</Text>
          
          <View style={styles.buttonContainer}>
            <Button
              onPress={() => navigation.goBack()}
              variant="default"
            >
              Go Back
            </Button>
            
            <Button
              onPress={() => navigation.navigate('MainTabs' as never)}
              variant="outline"
            >
              Go to Home
            </Button>
          </View>
        </View>
      </Card>
    </View>
  );
}
