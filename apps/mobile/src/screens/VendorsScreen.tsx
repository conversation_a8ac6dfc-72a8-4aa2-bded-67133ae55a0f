import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
  ScrollView,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Dimensions,
  Modal,
  SafeAreaView,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';
import { useFavorites } from '../providers/FavoritesProvider';
import { useSearch } from '../providers/SearchProvider';
import { graphqlService } from '../services/graphqlService';
import { Card, CardContent, CardHeader, CardTitle, Button, Input, Badge } from '../components/ui';
import { Header } from '../components/Header';
import DateTimePicker from '@react-native-community/datetimepicker';

interface Vendor {
  id: string;
  name: string;
  category: string;
  city: string;
  state: string;
  rating: number;
  reviewCount: number;
  priceRange: string;
  startingPrice?: number;
  profilePhoto?: string;
  verified: boolean;
  featured: boolean;
  description: string;
  experience?: number;
  isAvailable?: boolean;
  availableDates?: string[];
}

interface Category {
  icon: string;
  name: string;
  value: string;
}

export default function VendorsScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { isFavorite, addToFavorites, removeFromFavorites } = useFavorites();
  const { addToSearchHistory } = useSearch();

  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedCity, setSelectedCity] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [selectedRating, setSelectedRating] = useState('all');
  const [selectedPriceRange, setSelectedPriceRange] = useState('all');
  const [selectedExperience, setSelectedExperience] = useState('all');
  const [selectedAvailability, setSelectedAvailability] = useState('all');
  const [sortBy, setSortBy] = useState('rating');
  const [showFilters, setShowFilters] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(9);

  const { width } = Dimensions.get('window');

  const categories: Category[] = [
    { icon: 'camera', name: 'Photography', value: 'photography' },
    { icon: 'musical-notes', name: 'Music', value: 'music' },
    { icon: 'restaurant', name: 'Catering', value: 'catering' },
    { icon: 'color-palette', name: 'Decoration', value: 'decoration' },
    { icon: 'heart', name: 'Bridal Wear', value: 'bridal-wear' },
    { icon: 'diamond', name: 'Jewelry', value: 'jewelry' },
  ];

  const sortOptions = [
    { label: 'Rating', value: 'rating' },
    { label: 'Price: Low to High', value: 'price-low' },
    { label: 'Price: High to Low', value: 'price-high' },
    { label: 'Experience', value: 'experience' },
    { label: 'Name', value: 'name' },
  ];

  // Handle route parameters
  useEffect(() => {
    const params = route.params as any;
    if (params) {
      if (params.category) {
        setSelectedCategory(params.category);
      }
      if (params.search) {
        setSearchText(params.search);
      }
      if (params.location) {
        setSelectedCity(params.location);
      }
      if (params.city && params.state) {
        setSelectedCity(`${params.city}, ${params.state}`);
      }
      if (params.date) {
        setSelectedDate(new Date(params.date));
      }
    }
  }, [route.params]);

  useEffect(() => {
    loadVendors();
  }, [selectedCategory]);

  const loadVendors = async () => {
    try {
      setLoading(true);

      // Load all vendors first, then filter client-side
      const result = await graphqlService.listVendors({}, 100);
      setVendors(result.items);
    } catch (error) {
      console.error('Error loading vendors:', error);
      Alert.alert('Error', 'Failed to load vendors');
    } finally {
      setLoading(false);
    }
  };

  // Get unique cities from vendors
  const getUniqueCities = () => {
    const cities = vendors.map(v => v.city).filter(Boolean);
    return [...new Set(cities)];
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSearchText('');
    setSelectedCity('');
    setSelectedDate(undefined);
    setSelectedCategory('');
    setSelectedRating('all');
    setSelectedPriceRange('all');
    setSelectedExperience('all');
    setSelectedAvailability('all');
    setSortBy('rating');
  };

  // Check if any filters are active
  const hasActiveFilters = () => {
    return searchText || selectedCity || selectedCategory || selectedRating !== 'all' ||
           selectedPriceRange !== 'all' || selectedExperience !== 'all' ||
           selectedAvailability !== 'all';
  };

  // Get vendor image with fallback
  const getVendorImage = (vendor: Vendor) => {
    if (imageErrors.has(vendor.id)) {
      return 'https://via.placeholder.com/300x200?text=No+Image';
    }

    const profilePhoto = vendor.profilePhoto;
    if (profilePhoto && profilePhoto.trim() !== '' && profilePhoto !== 'undefined' && profilePhoto !== 'null') {
      return profilePhoto;
    }

    return 'https://via.placeholder.com/300x200?text=No+Image';
  };

  // Handle image load errors
  const handleImageError = (vendorId: string) => {
    setImageErrors(prev => new Set([...prev, vendorId]));
  };

  // Filter vendors based on all criteria
  const filteredVendors = vendors.filter(vendor => {
    const matchesSearch = !searchText ||
      vendor.name.toLowerCase().includes(searchText.toLowerCase()) ||
      vendor.description?.toLowerCase().includes(searchText.toLowerCase()) ||
      vendor.category?.toLowerCase().includes(searchText.toLowerCase());

    const matchesCity = !selectedCity ||
      vendor.city.toLowerCase().includes(selectedCity.toLowerCase());

    const matchesCategory = !selectedCategory || (() => {
      const vendorCat = vendor.category?.toLowerCase() || '';
      const selectedCat = selectedCategory.toLowerCase();

      // Direct match
      if (vendorCat === selectedCat) {
        return true;
      }

      // Handle category variations
      if (selectedCat === 'photography' && (vendorCat.includes('photo') || vendorCat.includes('camera'))) {
        return true;
      }
      if (selectedCat === 'music' && (vendorCat.includes('music') || vendorCat.includes('dj') || vendorCat.includes('band'))) return true;
      if (selectedCat === 'catering' && (vendorCat.includes('cater') || vendorCat.includes('food'))) return true;
      if (selectedCat === 'decoration' && (vendorCat.includes('decor') || vendorCat.includes('flower'))) return true;
      if (selectedCat === 'bridal-wear' && (vendorCat.includes('bridal') || vendorCat.includes('dress'))) return true;
      if (selectedCat === 'jewelry' && (vendorCat.includes('jewel') || vendorCat.includes('ornament'))) return true;

      return false;
    })();

    const matchesRating = selectedRating === 'all' || (() => {
      const rating = vendor.rating || 0;
      switch (selectedRating) {
        case '4-plus': return rating >= 4;
        case '3-plus': return rating >= 3;
        case '2-plus': return rating >= 2;
        default: return true;
      }
    })();

    const matchesPriceRange = selectedPriceRange === 'all' || (() => {
      const price = vendor.startingPrice || 0;
      switch (selectedPriceRange) {
        case 'budget': return price < 25000;
        case 'mid-range': return price >= 25000 && price <= 75000;
        case 'premium': return price > 75000;
        default: return true;
      }
    })();

    const matchesExperience = selectedExperience === 'all' || (() => {
      const experience = vendor.experience || 0;
      switch (selectedExperience) {
        case '1-3': return experience >= 1 && experience <= 3;
        case '4-7': return experience >= 4 && experience <= 7;
        case '8-plus': return experience >= 8;
        default: return true;
      }
    })();

    const matchesAvailability = selectedAvailability === 'all' || (() => {
      switch (selectedAvailability) {
        case 'available': return vendor.isAvailable;
        case 'busy': return !vendor.isAvailable;
        default: return true;
      }
    })();

    const matchesDate = !selectedDate || (() => {
      if (vendor.availableDates && Array.isArray(vendor.availableDates)) {
        const selectedDateStr = selectedDate.toISOString().split('T')[0];
        return vendor.availableDates.includes(selectedDateStr);
      }
      return true;
    })();

    return matchesSearch && matchesCity && matchesCategory && matchesRating &&
           matchesPriceRange && matchesExperience && matchesAvailability && matchesDate;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'rating':
        return (b.rating || 0) - (a.rating || 0);
      case 'price-low':
        return (a.startingPrice || 0) - (b.startingPrice || 0);
      case 'price-high':
        return (b.startingPrice || 0) - (a.startingPrice || 0);
      case 'experience':
        return (b.experience || 0) - (a.experience || 0);
      case 'name':
      default:
        return a.name.localeCompare(b.name);
    }
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredVendors.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedVendors = filteredVendors.slice(startIndex, endIndex);

  // Reset and reload data when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchText, selectedCity, selectedDate, selectedCategory, selectedRating, selectedPriceRange, selectedExperience, selectedAvailability]);

  // Pagination functions
  const goToPage = (page: number) => {
    setCurrentPage(page);
  };

  const goToPrevious = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  const goToNext = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadVendors();
    setRefreshing(false);
  };

  const handleSearch = () => {
    if (searchText.trim()) {
      addToSearchHistory(searchText, 'vendor');
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setSelectedDate(selectedDate);
    }
  };

  const renderCategoryFilter = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.categoryFilter}
      contentContainerStyle={styles.categoryFilterContent}
    >
      <TouchableOpacity
        style={[
          styles.categoryItem,
          !selectedCategory && styles.categoryItemActive,
          { backgroundColor: !selectedCategory ? theme.colors.primary : theme.colors.surface }
        ]}
        onPress={() => setSelectedCategory('')}
      >
        <Ionicons
          name="apps"
          size={20}
          color={!selectedCategory ? theme.colors.primaryForeground : theme.colors.text}
        />
        <Text style={[
          styles.categoryText,
          { color: !selectedCategory ? theme.colors.primaryForeground : theme.colors.text }
        ]}>
          All Categories
        </Text>
      </TouchableOpacity>

      {categories.map((category) => (
        <TouchableOpacity
          key={category.value}
          style={[
            styles.categoryItem,
            selectedCategory === category.value && styles.categoryItemActive,
            { backgroundColor: selectedCategory === category.value ? theme.colors.primary : theme.colors.surface }
          ]}
          onPress={() => setSelectedCategory(category.value)}
        >
          <Ionicons
            name={category.icon as any}
            size={20}
            color={selectedCategory === category.value ? theme.colors.primaryForeground : theme.colors.text}
          />
          <Text style={[
            styles.categoryText,
            { color: selectedCategory === category.value ? theme.colors.primaryForeground : theme.colors.text }
          ]}>
            {category.name}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const handleFavoriteToggle = async (vendor: Vendor) => {
    if (isFavorite(vendor.id, 'vendor')) {
      await removeFromFavorites(vendor.id, 'vendor');
    } else {
      await addToFavorites({
        id: vendor.id,
        type: 'vendor',
        name: vendor.name,
        image: vendor.profilePhoto,
        rating: vendor.rating,
        location: `${vendor.city}, ${vendor.state}`,
        price: vendor.priceRange,
      });
    }
  };

  const renderVendor = ({ item }: { item: Vendor }) => (
    <TouchableOpacity
      onPress={() => navigation.navigate('VendorDetail', { vendorId: item.id })}
      style={styles.vendorCardContainer}
    >
      <Card style={styles.vendorCard}>
        <View style={styles.vendorImageContainer}>
          <Image
            source={{ uri: getVendorImage(item) }}
            style={styles.vendorImage}
            resizeMode="cover"
            onError={() => handleImageError(item.id)}
          />

          {/* Featured Badge */}
          {item.featured && (
            <View style={[styles.featuredBadge, { backgroundColor: theme.colors.accent }]}>
              <Text style={styles.featuredText}>Featured</Text>
            </View>
          )}

          {/* Verified Badge */}
          {item.verified && (
            <View style={[styles.verifiedBadge, { backgroundColor: theme.colors.success }]}>
              <Ionicons name="checkmark-circle" size={16} color="#fff" />
            </View>
          )}

          {/* Favorite Button */}
          <TouchableOpacity
            style={styles.favoriteButton}
            onPress={(e) => {
              e.stopPropagation();
              handleFavoriteToggle(item);
            }}
          >
            <Ionicons
              name={isFavorite(item.id, 'vendor') ? 'heart' : 'heart-outline'}
              size={20}
              color={isFavorite(item.id, 'vendor') ? theme.colors.error : '#fff'}
            />
          </TouchableOpacity>
        </View>

        <CardContent style={styles.vendorInfo}>
          <View style={styles.vendorHeader}>
            <Text style={[styles.vendorName, { color: theme.colors.text }]} numberOfLines={2}>
              {item.name}
            </Text>
          </View>

          <View style={styles.categoryContainer}>
            <Text style={[styles.categoryText, { color: theme.colors.primary }]}>
              {item.category}
            </Text>
          </View>

          <View style={styles.locationContainer}>
            <Ionicons name="location-outline" size={14} color={theme.colors.textSecondary} />
            <Text style={[styles.vendorLocation, { color: theme.colors.textSecondary }]} numberOfLines={1}>
              {item.city}, {item.state}
            </Text>
          </View>

          {item.description && (
            <Text style={[styles.vendorDescription, { color: theme.colors.textSecondary }]} numberOfLines={2}>
              {item.description}
            </Text>
          )}

          <View style={styles.vendorFooter}>
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={14} color={theme.colors.accent} />
              <Text style={[styles.rating, { color: theme.colors.text }]}>
                {item.rating ? item.rating.toFixed(1) : '0.0'}
              </Text>
              <Text style={[styles.reviewCount, { color: theme.colors.textSecondary }]}>
                ({item.reviewCount || 0})
              </Text>
            </View>

            <View style={styles.priceContainer}>
              <Text style={[styles.priceLabel, { color: theme.colors.textSecondary }]}>
                Starting from
              </Text>
              <Text style={[styles.priceRange, { color: theme.colors.primary }]}>
                {item.startingPrice ? `₹${item.startingPrice.toLocaleString()}` : item.priceRange}
              </Text>
            </View>
          </View>

          {/* Experience and Availability */}
          <View style={styles.vendorMeta}>
            {item.experience && (
              <View style={styles.metaItem}>
                <Ionicons name="time-outline" size={12} color={theme.colors.textSecondary} />
                <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
                  {item.experience}+ years
                </Text>
              </View>
            )}

            <View style={[styles.availabilityBadge, {
              backgroundColor: item.isAvailable ? theme.colors.success + '20' : theme.colors.error + '20'
            }]}>
              <Text style={[styles.availabilityText, {
                color: item.isAvailable ? theme.colors.success : theme.colors.error
              }]}>
                {item.isAvailable ? 'Available' : 'Busy'}
              </Text>
            </View>
          </View>
        </CardContent>
      </Card>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Search and Filters Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={[styles.searchBar, { backgroundColor: theme.colors.background }]}>
            <Ionicons name="search-outline" size={20} color={theme.colors.textSecondary} />
            <TextInput
              style={[styles.searchInput, { color: theme.colors.text }]}
              placeholder="Search vendors..."
              placeholderTextColor={theme.colors.textSecondary}
              value={searchText}
              onChangeText={setSearchText}
              onSubmitEditing={handleSearch}
              returnKeyType="search"
            />
            {searchText.length > 0 && (
              <TouchableOpacity onPress={() => setSearchText('')} style={{ marginLeft: 4 }}>
                <Ionicons name="close-circle" size={18} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>

          {/* Filter Button */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.filterButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => setShowFilters(!showFilters)}
            >
              <Ionicons name="options-outline" size={20} color="white" />
              {hasActiveFilters() && <View style={{ position: 'absolute', top: 6, right: 6, width: 8, height: 8, borderRadius: 4, backgroundColor: theme.colors.accent }} />}
            </TouchableOpacity>
          </View>
        </View>

        {/* Location and Date Filters */}
        <View style={styles.quickFilters}>
          <TouchableOpacity style={[styles.quickFilterButton, { backgroundColor: theme.colors.background }]}>
            <Ionicons name="location-outline" size={16} color={theme.colors.textSecondary} />
            <TextInput
              style={[styles.quickFilterText, { color: theme.colors.text }]}
              placeholder="Enter city"
              placeholderTextColor={theme.colors.textSecondary}
              value={selectedCity}
              onChangeText={setSelectedCity}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickFilterButton, { backgroundColor: theme.colors.background }]}
            onPress={() => setShowDatePicker(true)}
          >
            <Ionicons name="calendar-outline" size={16} color={theme.colors.textSecondary} />
            <Text style={[styles.quickFilterText, { color: theme.colors.text }]}>
              {selectedDate ? selectedDate.toLocaleDateString() : 'Select Date'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Category Filter */}
      <View style={styles.categoryContainer}>
        {renderCategoryFilter()}
      </View>

      {/* Results Header */}
      <View style={[styles.resultsHeader, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.resultsInfo}>
          <Text style={[styles.resultsCount, { color: theme.colors.text }]}>
            {filteredVendors.length} vendors found
          </Text>
          {hasActiveFilters() && (
            <TouchableOpacity onPress={clearAllFilters} style={styles.clearFilters}>
              <Text style={[styles.clearFiltersText, { color: theme.colors.primary }]}>
                Clear all filters
              </Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.sortContainer}>
          <Text style={[styles.sortLabel, { color: theme.colors.textSecondary }]}>Sort:</Text>
          <TouchableOpacity
            style={[styles.sortButton, { borderColor: theme.colors.border }]}
            onPress={() => {
              // Show sort options modal or picker
            }}
          >
            <Text style={[styles.sortButtonText, { color: theme.colors.text }]}>
              {sortOptions.find(opt => opt.value === sortBy)?.label || 'Rating'}
            </Text>
            <Ionicons name="chevron-down" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>
      {/* Vendors List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
            Loading vendors...
          </Text>
        </View>
      ) : (
        <ScrollView
          style={styles.vendorsContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        >
          {paginatedVendors.length > 0 ? (
            <>
              <View style={styles.vendorsList}>
                {paginatedVendors.map((vendor) => (
                  <View key={vendor.id} style={styles.vendorItem}>
                    {renderVendor({ item: vendor })}
                  </View>
                ))}
              </View>

              {/* Pagination */}
              {totalPages > 1 && (
                <View style={styles.paginationContainer}>
                  <TouchableOpacity
                    style={[
                      styles.paginationButton,
                      { backgroundColor: currentPage === 1 ? theme.colors.border : theme.colors.primary }
                    ]}
                    onPress={goToPrevious}
                    disabled={currentPage === 1}
                  >
                    <Ionicons
                      name="chevron-back"
                      size={20}
                      color={currentPage === 1 ? theme.colors.textSecondary : '#fff'}
                    />
                  </TouchableOpacity>

                  <View style={styles.pageNumbers}>
                    <Text style={[styles.pageInfo, { color: theme.colors.text }]}>
                      Page {currentPage} of {totalPages}
                    </Text>
                  </View>

                  <TouchableOpacity
                    style={[
                      styles.paginationButton,
                      { backgroundColor: currentPage === totalPages ? theme.colors.border : theme.colors.primary }
                    ]}
                    onPress={goToNext}
                    disabled={currentPage === totalPages}
                  >
                    <Ionicons
                      name="chevron-forward"
                      size={20}
                      color={currentPage === totalPages ? theme.colors.textSecondary : '#fff'}
                    />
                  </TouchableOpacity>
                </View>
              )}
            </>
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons name="people-outline" size={64} color={theme.colors.textSecondary} />
              <Text style={[styles.emptyText, { color: theme.colors.text }]}>
                No vendors found
              </Text>
              <Text style={[styles.emptySubtext, { color: theme.colors.textSecondary }]}>
                Try adjusting your search or filters
              </Text>
              {hasActiveFilters() && (
                <TouchableOpacity onPress={clearAllFilters} style={styles.clearFiltersButton}>
                  <Text style={[styles.clearFiltersButtonText, { color: theme.colors.primary }]}>
                    Clear all filters
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </ScrollView>
      )}

      {/* Date Picker Modal */}
      {showDatePicker && (
        <DateTimePicker
          value={selectedDate || new Date()}
          mode="date"
          display="default"
          onChange={handleDateChange}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 12,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 0,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  quickFilters: {
    flexDirection: 'row',
    gap: 12,
  },
  quickFilterButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  quickFilterText: {
    flex: 1,
    fontSize: 14,
  },
  filterButton: {
    padding: 10,
    borderRadius: 8,
    position: 'relative',
  },
  filterIndicator: {
    position: 'absolute',
    top: 6,
    right: 6,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#F6C244',
  },
  headerFilters: {
    flexDirection: 'row',
    gap: 16,
  },
  headerFilterItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  headerFilterText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  categoryContainer: {
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  categoryFilter: {
    paddingVertical: 12,
  },
  categoryFilterContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    gap: 8,
    marginRight: 8,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  categoryItemActive: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  resultsInfo: {
    flex: 1,
  },
  resultsCount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  clearFilters: {
    alignSelf: 'flex-start',
  },
  clearFiltersText: {
    fontSize: 14,
    fontWeight: '500',
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sortLabel: {
    fontSize: 14,
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 6,
    gap: 6,
  },
  sortButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  vendorsContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  vendorsList: {
    padding: 16,
  },
  vendorItem: {
    marginBottom: 16,
  },
  vendorCardContainer: {
    width: '100%',
  },
  vendorCard: {
    overflow: 'hidden',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  vendorImageContainer: {
    position: 'relative',
    height: 200,
  },
  vendorImage: {
    width: '100%',
    height: '100%',
  },
  featuredBadge: {
    position: 'absolute',
    top: 12,
    left: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  featuredText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  verifiedBadge: {
    position: 'absolute',
    top: 12,
    left: 12,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  vendorInfo: {
    padding: 16,
  },
  vendorHeader: {
    marginBottom: 8,
  },
  vendorName: {
    fontSize: 18,
    fontWeight: '700',
    lineHeight: 24,
  },
  categoryContainer: {
    marginBottom: 8,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 8,
  },
  vendorLocation: {
    fontSize: 14,
    flex: 1,
  },
  vendorDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  vendorFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    marginBottom: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rating: {
    fontSize: 14,
    fontWeight: '600',
  },
  reviewCount: {
    fontSize: 12,
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  priceLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  priceRange: {
    fontSize: 16,
    fontWeight: '700',
  },
  vendorMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
  },
  availabilityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  availabilityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
    gap: 16,
  },
  paginationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pageNumbers: {
    flex: 1,
    alignItems: 'center',
  },
  pageInfo: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
    paddingHorizontal: 32,
    gap: 16,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  clearFiltersButton: {
    marginTop: 16,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(97, 15, 19, 0.1)',
  },
  clearFiltersButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
