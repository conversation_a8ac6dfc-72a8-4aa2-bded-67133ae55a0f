import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Linking,
  Alert 
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';
import { Header } from '../../components/Header';
import { Card, Input, Button } from '../../components/ui';

interface ContactMethod {
  id: string;
  title: string;
  subtitle: string;
  icon: string;
  action: () => void;
  color: string;
}

export default function ContactScreen() {
  const { theme } = useTheme();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [loading, setLoading] = useState(false);

  const contactMethods: ContactMethod[] = [
    {
      id: 'phone',
      title: 'Call Us',
      subtitle: '+91 98765 43210',
      icon: 'call',
      color: '#4CAF50',
      action: () => Linking.openURL('tel:+919876543210')
    },
    {
      id: 'whatsapp',
      title: 'WhatsApp',
      subtitle: 'Chat with us instantly',
      icon: 'logo-whatsapp',
      color: '#25D366',
      action: () => Linking.openURL('https://wa.me/919876543210')
    },
    {
      id: 'email',
      title: 'Email Us',
      subtitle: '<EMAIL>',
      icon: 'mail',
      color: '#2196F3',
      action: () => Linking.openURL('mailto:<EMAIL>')
    },
    {
      id: 'location',
      title: 'Visit Us',
      subtitle: 'Chennai, Tamil Nadu',
      icon: 'location',
      color: '#FF9800',
      action: () => Linking.openURL('https://maps.google.com/?q=Chennai,Tamil+Nadu')
    }
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    if (!formData.name || !formData.email || !formData.message) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'Message Sent!', 
        'Thank you for contacting us. We will get back to you within 24 hours.',
        [{ text: 'OK', onPress: () => {
          setFormData({
            name: '',
            email: '',
            phone: '',
            subject: '',
            message: ''
          });
        }}]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to send message. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderContactMethod = (method: ContactMethod) => (
    <TouchableOpacity 
      key={method.id}
      style={[styles.contactMethodCard, { borderColor: theme.colors.border }]}
      onPress={method.action}
    >
      <View style={[styles.contactMethodIcon, { backgroundColor: method.color }]}>
        <Ionicons name={method.icon as any} size={24} color="white" />
      </View>
      <View style={styles.contactMethodInfo}>
        <Text style={[styles.contactMethodTitle, { color: theme.colors.text }]}>
          {method.title}
        </Text>
        <Text style={[styles.contactMethodSubtitle, { color: theme.colors.textSecondary }]}>
          {method.subtitle}
        </Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Contact Us" showBack />
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Contact Methods */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Get in Touch
          </Text>
          <Text style={[styles.sectionSubtitle, { color: theme.colors.textSecondary }]}>
            Choose your preferred way to contact us
          </Text>
          
          <View style={styles.contactMethodsContainer}>
            {contactMethods.map(renderContactMethod)}
          </View>
        </View>

        {/* Contact Form */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Send us a Message
          </Text>
          <Text style={[styles.sectionSubtitle, { color: theme.colors.textSecondary }]}>
            Fill out the form below and we'll get back to you soon
          </Text>
          
          <Card style={styles.formCard}>
            <View style={styles.formContainer}>
              <Input
                label="Full Name *"
                placeholder="Enter your full name"
                value={formData.name}
                onChangeText={(value) => handleInputChange('name', value)}
                leftIcon={<Ionicons name="person" size={20} color={theme.colors.textSecondary} />}
              />
              
              <Input
                label="Email Address *"
                placeholder="Enter your email"
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                keyboardType="email-address"
                autoCapitalize="none"
                leftIcon={<Ionicons name="mail" size={20} color={theme.colors.textSecondary} />}
              />
              
              <Input
                label="Phone Number"
                placeholder="Enter your phone number"
                value={formData.phone}
                onChangeText={(value) => handleInputChange('phone', value)}
                keyboardType="phone-pad"
                leftIcon={<Ionicons name="call" size={20} color={theme.colors.textSecondary} />}
              />
              
              <Input
                label="Subject"
                placeholder="What is this regarding?"
                value={formData.subject}
                onChangeText={(value) => handleInputChange('subject', value)}
                leftIcon={<Ionicons name="chatbubble" size={20} color={theme.colors.textSecondary} />}
              />
              
              <Input
                label="Message *"
                placeholder="Tell us how we can help you..."
                value={formData.message}
                onChangeText={(value) => handleInputChange('message', value)}
                multiline
                numberOfLines={4}
                style={styles.messageInput}
                leftIcon={<Ionicons name="document-text" size={20} color={theme.colors.textSecondary} />}
              />
              
              <Button 
                onPress={handleSubmit}
                loading={loading}
                style={styles.submitButton}
              >
                Send Message
              </Button>
            </View>
          </Card>
        </View>

        {/* Business Hours */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Business Hours
          </Text>
          
          <Card style={styles.hoursCard}>
            <View style={styles.hoursContainer}>
              <View style={styles.hoursRow}>
                <Text style={[styles.dayText, { color: theme.colors.text }]}>Monday - Friday</Text>
                <Text style={[styles.timeText, { color: theme.colors.textSecondary }]}>9:00 AM - 7:00 PM</Text>
              </View>
              <View style={styles.hoursRow}>
                <Text style={[styles.dayText, { color: theme.colors.text }]}>Saturday</Text>
                <Text style={[styles.timeText, { color: theme.colors.textSecondary }]}>10:00 AM - 6:00 PM</Text>
              </View>
              <View style={styles.hoursRow}>
                <Text style={[styles.dayText, { color: theme.colors.text }]}>Sunday</Text>
                <Text style={[styles.timeText, { color: theme.colors.textSecondary }]}>11:00 AM - 5:00 PM</Text>
              </View>
            </View>
          </Card>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  contactMethodsContainer: {
    gap: 12,
  },
  contactMethodCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    borderWidth: 1,
  },
  contactMethodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  contactMethodInfo: {
    flex: 1,
  },
  contactMethodTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  contactMethodSubtitle: {
    fontSize: 14,
  },
  formCard: {
    padding: 0,
  },
  formContainer: {
    padding: 20,
    gap: 16,
  },
  messageInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    marginTop: 8,
  },
  hoursCard: {
    padding: 0,
  },
  hoursContainer: {
    padding: 20,
  },
  hoursRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  dayText: {
    fontSize: 16,
    fontWeight: '500',
  },
  timeText: {
    fontSize: 14,
  },
});
