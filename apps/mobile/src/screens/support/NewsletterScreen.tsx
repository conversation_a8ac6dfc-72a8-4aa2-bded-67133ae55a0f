import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';
import { Header } from '../../components/Header';
import { Card, Input, Button, Badge } from '../../components/ui';

export default function NewsletterScreen() {
  const { theme } = useTheme();
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [selectedPreferences, setSelectedPreferences] = useState<string[]>([]);
  const [isSubscribed, setIsSubscribed] = useState(false);

  const preferences = [
    { id: 'wedding_tips', label: 'Wedding Planning Tips', icon: 'bulb' },
    { id: 'vendor_updates', label: 'New Vendor Updates', icon: 'business' },
    { id: 'special_offers', label: 'Special Offers & Discounts', icon: 'pricetag' },
    { id: 'real_weddings', label: 'Real Wedding Stories', icon: 'heart' },
    { id: 'trends', label: 'Latest Wedding Trends', icon: 'trending-up' },
    { id: 'seasonal', label: 'Seasonal Recommendations', icon: 'calendar' },
  ];

  const handlePreferenceToggle = (preferenceId: string) => {
    setSelectedPreferences(prev => 
      prev.includes(preferenceId)
        ? prev.filter(id => id !== preferenceId)
        : [...prev, preferenceId]
    );
  };

  const handleSubscribe = () => {
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter your email address');
      return;
    }

    if (selectedPreferences.length === 0) {
      Alert.alert('Error', 'Please select at least one preference');
      return;
    }

    // TODO: Implement actual newsletter subscription API call
    setIsSubscribed(true);
    Alert.alert(
      'Success!', 
      'Thank you for subscribing to our newsletter. You will receive updates based on your preferences.',
      [{ text: 'OK', onPress: () => {} }]
    );
  };

  const handleUnsubscribe = () => {
    Alert.alert(
      'Unsubscribe',
      'Are you sure you want to unsubscribe from our newsletter?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Unsubscribe',
          style: 'destructive',
          onPress: () => {
            setIsSubscribed(false);
            setEmail('');
            setName('');
            setSelectedPreferences([]);
            Alert.alert('Unsubscribed', 'You have been unsubscribed from our newsletter.');
          },
        },
      ]
    );
  };

  if (isSubscribed) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header title="Newsletter" showBack />
        
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Card style={styles.successCard}>
            <View style={styles.successIcon}>
              <Ionicons name="checkmark-circle" size={64} color={theme.colors.success} />
            </View>
            <Text style={[styles.successTitle, { color: theme.colors.text }]}>
              You're Subscribed!
            </Text>
            <Text style={[styles.successMessage, { color: theme.colors.textSecondary }]}>
              Thank you for subscribing to Thirumanam360 newsletter. You'll receive updates about:
            </Text>
            
            <View style={styles.subscribedPreferences}>
              {selectedPreferences.map(prefId => {
                const pref = preferences.find(p => p.id === prefId);
                return pref ? (
                  <View key={prefId} style={styles.subscribedPref}>
                    <Ionicons name={pref.icon as any} size={16} color={theme.colors.primary} />
                    <Text style={[styles.subscribedPrefText, { color: theme.colors.text }]}>
                      {pref.label}
                    </Text>
                  </View>
                ) : null;
              })}
            </View>

            <Button
              onPress={handleUnsubscribe}
              variant="outline"
              style={styles.unsubscribeButton}
            >
              <Text style={[styles.unsubscribeButtonText, { color: theme.colors.destructive }]}>
                Unsubscribe
              </Text>
            </Button>
          </Card>
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Newsletter" showBack />
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.headerCard}>
          <View style={styles.headerIcon}>
            <Ionicons name="mail" size={48} color={theme.colors.primary} />
          </View>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Stay Updated with Thirumanam360
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            Get the latest wedding planning tips, vendor updates, and exclusive offers delivered to your inbox.
          </Text>
        </Card>

        <Card style={styles.formCard}>
          <Text style={[styles.formTitle, { color: theme.colors.text }]}>
            Subscribe to Our Newsletter
          </Text>
          
          <View style={styles.inputContainer}>
            <Input
              placeholder="Your email address"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              leftIcon={<Ionicons name="mail" size={20} color={theme.colors.textSecondary} />}
            />
          </View>

          <View style={styles.inputContainer}>
            <Input
              placeholder="Your name (optional)"
              value={name}
              onChangeText={setName}
              leftIcon={<Ionicons name="person" size={20} color={theme.colors.textSecondary} />}
            />
          </View>

          <Text style={[styles.preferencesTitle, { color: theme.colors.text }]}>
            What would you like to receive?
          </Text>
          
          <View style={styles.preferencesContainer}>
            {preferences.map(preference => (
              <Card
                key={preference.id}
                style={[
                  styles.preferenceCard,
                  selectedPreferences.includes(preference.id) && styles.preferenceCardSelected,
                  { borderColor: selectedPreferences.includes(preference.id) ? theme.colors.primary : theme.colors.border }
                ]}
                onPress={() => handlePreferenceToggle(preference.id)}
              >
                <View style={styles.preferenceContent}>
                  <Ionicons 
                    name={preference.icon as any} 
                    size={24} 
                    color={selectedPreferences.includes(preference.id) ? theme.colors.primary : theme.colors.textSecondary} 
                  />
                  <Text style={[
                    styles.preferenceLabel,
                    { color: selectedPreferences.includes(preference.id) ? theme.colors.primary : theme.colors.text }
                  ]}>
                    {preference.label}
                  </Text>
                  {selectedPreferences.includes(preference.id) && (
                    <Ionicons name="checkmark-circle" size={20} color={theme.colors.primary} />
                  )}
                </View>
              </Card>
            ))}
          </View>

          <Button
            onPress={handleSubscribe}
            style={styles.subscribeButton}
          >
            <Ionicons name="mail" size={20} color="white" />
            <Text style={styles.subscribeButtonText}>Subscribe Now</Text>
          </Button>
        </Card>

        <Card style={styles.benefitsCard}>
          <Text style={[styles.benefitsTitle, { color: theme.colors.text }]}>
            Why Subscribe?
          </Text>
          
          <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>
              <Ionicons name="star" size={16} color={theme.colors.accent} />
              <Text style={[styles.benefitText, { color: theme.colors.textSecondary }]}>
                Exclusive wedding planning tips from experts
              </Text>
            </View>
            
            <View style={styles.benefitItem}>
              <Ionicons name="star" size={16} color={theme.colors.accent} />
              <Text style={[styles.benefitText, { color: theme.colors.textSecondary }]}>
                Early access to special offers and discounts
              </Text>
            </View>
            
            <View style={styles.benefitItem}>
              <Ionicons name="star" size={16} color={theme.colors.accent} />
              <Text style={[styles.benefitText, { color: theme.colors.textSecondary }]}>
                Latest trends and inspiration for your big day
              </Text>
            </View>
            
            <View style={styles.benefitItem}>
              <Ionicons name="star" size={16} color={theme.colors.accent} />
              <Text style={[styles.benefitText, { color: theme.colors.textSecondary }]}>
                Updates on new vendors and services
              </Text>
            </View>
          </View>
        </Card>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  headerCard: {
    marginVertical: 16,
    padding: 24,
    alignItems: 'center',
  },
  headerIcon: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  formCard: {
    marginBottom: 16,
    padding: 20,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 16,
  },
  preferencesTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
    marginTop: 8,
  },
  preferencesContainer: {
    gap: 8,
    marginBottom: 24,
  },
  preferenceCard: {
    padding: 12,
    borderWidth: 1,
  },
  preferenceCardSelected: {
    backgroundColor: 'rgba(97, 15, 19, 0.05)',
  },
  preferenceContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  preferenceLabel: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
  subscribeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 14,
  },
  subscribeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  benefitsCard: {
    marginBottom: 32,
    padding: 20,
  },
  benefitsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  benefitsList: {
    gap: 12,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
  },
  benefitText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 18,
  },
  successCard: {
    marginVertical: 16,
    padding: 32,
    alignItems: 'center',
  },
  successIcon: {
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  successMessage: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  subscribedPreferences: {
    gap: 8,
    marginBottom: 32,
    alignSelf: 'stretch',
  },
  subscribedPref: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 4,
  },
  subscribedPrefText: {
    fontSize: 14,
    fontWeight: '500',
  },
  unsubscribeButton: {
    paddingVertical: 12,
  },
  unsubscribeButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
