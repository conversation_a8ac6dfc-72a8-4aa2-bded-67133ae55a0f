import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../providers/ThemeProvider';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { Button } from '../../components/ui/Button';

export default function HelpScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);

  const helpCategories = [
    {
      title: 'Getting Started',
      icon: '🚀',
      description: 'Learn the basics of using Thirumanam 360',
      onPress: () => {},
    },
    {
      title: 'Booking Services',
      icon: '📅',
      description: 'How to book vendors and venues',
      onPress: () => {},
    },
    {
      title: 'Payment & Billing',
      icon: '💳',
      description: 'Payment methods and billing questions',
      onPress: () => {},
    },
    {
      title: 'Account Management',
      icon: '👤',
      description: 'Manage your profile and settings',
      onPress: () => {},
    },
  ];

  const faqs = [
    {
      id: '1',
      question: 'How do I book a wedding vendor?',
      answer: 'To book a vendor, browse our vendor listings, select your preferred vendor, choose your service date, and complete the booking form. You\'ll receive a confirmation email once the vendor accepts your booking.',
    },
    {
      id: '2',
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit/debit cards, UPI payments, net banking, and digital wallets. All payments are processed securely through Razorpay.',
    },
    {
      id: '3',
      question: 'Can I cancel or modify my booking?',
      answer: 'Yes, you can cancel or modify your booking up to 48 hours before the service date. Cancellation policies may vary by vendor. Check the vendor\'s specific terms for details.',
    },
    {
      id: '4',
      question: 'How do I become a vendor on Thirumanam 360?',
      answer: 'To join as a vendor, click on "Join as Vendor" on the login page, fill out the registration form with your business details, and submit required documents. Our team will review and approve your application.',
    },
    {
      id: '5',
      question: 'Is my personal information secure?',
      answer: 'Yes, we take data security seriously. All personal information is encrypted and stored securely. We never share your data with third parties without your consent.',
    },
  ];

  const filteredFAQs = faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: 16,
      gap: 20,
    },
    header: {
      alignItems: 'center',
      marginBottom: 8,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
    },
    searchContainer: {
      marginBottom: 8,
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 12,
    },
    categoriesGrid: {
      gap: 12,
    },
    categoryCard: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
    },
    categoryIcon: {
      fontSize: 32,
      marginRight: 16,
    },
    categoryContent: {
      flex: 1,
    },
    categoryTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 4,
    },
    categoryDescription: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    faqCard: {
      padding: 16,
      marginBottom: 8,
    },
    faqQuestion: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
      lineHeight: 22,
    },
    faqAnswer: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 20,
      marginTop: 8,
    },
    expandIcon: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      alignSelf: 'flex-end',
    },
    contactSection: {
      alignItems: 'center',
      padding: 20,
      backgroundColor: theme.colors.muted,
      borderRadius: 12,
    },
    contactTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
    },
    contactText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: 16,
      lineHeight: 20,
    },
    contactButtons: {
      flexDirection: 'row',
      gap: 12,
    },
    contactButton: {
      flex: 1,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>Help & Support</Text>
          <Text style={styles.subtitle}>
            Find answers to common questions or get in touch with our support team
          </Text>
        </View>

        <View style={styles.searchContainer}>
          <Input
            placeholder="Search for help..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        <View>
          <Text style={styles.sectionTitle}>Help Categories</Text>
          <View style={styles.categoriesGrid}>
            {helpCategories.map((category, index) => (
              <Card
                key={index}
                style={styles.categoryCard}
                onPress={category.onPress}
              >
                <Text style={styles.categoryIcon}>{category.icon}</Text>
                <View style={styles.categoryContent}>
                  <Text style={styles.categoryTitle}>{category.title}</Text>
                  <Text style={styles.categoryDescription}>
                    {category.description}
                  </Text>
                </View>
              </Card>
            ))}
          </View>
        </View>

        <View>
          <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
          {filteredFAQs.map((faq) => (
            <Card
              key={faq.id}
              style={styles.faqCard}
              onPress={() =>
                setExpandedFAQ(expandedFAQ === faq.id ? null : faq.id)
              }
            >
              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <Text style={styles.faqQuestion}>{faq.question}</Text>
                <Text style={styles.expandIcon}>
                  {expandedFAQ === faq.id ? '−' : '+'}
                </Text>
              </View>
              {expandedFAQ === faq.id && (
                <Text style={styles.faqAnswer}>{faq.answer}</Text>
              )}
            </Card>
          ))}
        </View>

        <View style={styles.contactSection}>
          <Text style={styles.contactTitle}>Still need help?</Text>
          <Text style={styles.contactText}>
            Can't find what you're looking for? Our support team is here to help you.
          </Text>
          <View style={styles.contactButtons}>
            <Button
              variant="outline"
              style={styles.contactButton}
              onPress={() => navigation.navigate('Contact' as never)}
            >
              Contact Us
            </Button>
            <Button
              style={styles.contactButton}
              onPress={() => {
                // TODO: Implement live chat
              }}
            >
              Live Chat
            </Button>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}
