import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';
import { Header } from '../../components/Header';
import { Card, Input, Badge } from '../../components/ui';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { AppStackParamList } from '../../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<AppStackParamList>;

interface Offer {
  id: string;
  title: string;
  description: string;
  discount: string;
  category: 'all' | 'decorations' | 'jewelry' | 'clothing' | 'photography' | 'catering';
  vendorName: string;
  vendorId: string;
  image: string;
  originalPrice?: number;
  discountedPrice?: number;
  validUntil: string;
  termsAndConditions: string[];
  isActive: boolean;
  usageCount: number;
  maxUsage?: number;
  code?: string;
}

export default function OffersScreen() {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const [offers, setOffers] = useState<Offer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<Offer['category']>('all');

  const categories = [
    { id: 'all', label: 'All Offers', icon: 'pricetag' },
    { id: 'decorations', label: 'Decorations', icon: 'flower' },
    { id: 'jewelry', label: 'Jewelry', icon: 'diamond' },
    { id: 'clothing', label: 'Clothing', icon: 'shirt' },
    { id: 'photography', label: 'Photography', icon: 'camera' },
    { id: 'catering', label: 'Catering', icon: 'restaurant' },
  ];

  useEffect(() => {
    loadOffers();
  }, []);

  const loadOffers = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockOffers: Offer[] = [
        {
          id: '1',
          title: '25% Off Wedding Decorations',
          description: 'Get 25% discount on all wedding decoration packages. Perfect for your dream wedding setup.',
          discount: '25% OFF',
          category: 'decorations',
          vendorName: 'Elegant Decorations',
          vendorId: 'vendor-1',
          image: 'https://images.unsplash.com/photo-1519741497674-611481863552?w=400',
          originalPrice: 150000,
          discountedPrice: 112500,
          validUntil: '2024-03-31T23:59:59Z',
          termsAndConditions: [
            'Valid for bookings made before March 31, 2024',
            'Minimum order value of ₹1,00,000',
            'Cannot be combined with other offers',
            'Valid for new customers only'
          ],
          isActive: true,
          usageCount: 45,
          maxUsage: 100,
          code: 'WEDDING25',
        },
        {
          id: '2',
          title: 'Bridal Jewelry Collection - 30% Off',
          description: 'Exclusive discount on our premium bridal jewelry collection. Limited time offer.',
          discount: '30% OFF',
          category: 'jewelry',
          vendorName: 'Golden Jewelry',
          vendorId: 'vendor-2',
          image: 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400',
          originalPrice: 85000,
          discountedPrice: 59500,
          validUntil: '2024-02-29T23:59:59Z',
          termsAndConditions: [
            'Valid on selected jewelry items only',
            'Offer valid till February 29, 2024',
            'Free home delivery included',
            'Exchange policy applicable'
          ],
          isActive: true,
          usageCount: 23,
          maxUsage: 50,
          code: 'BRIDAL30',
        },
        {
          id: '3',
          title: 'Designer Lehenga - Buy 1 Get 1 Free',
          description: 'Special offer on designer lehengas. Buy one and get another absolutely free.',
          discount: 'BOGO',
          category: 'clothing',
          vendorName: 'Designer Lehengas',
          vendorId: 'vendor-3',
          image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400',
          originalPrice: 45000,
          discountedPrice: 45000,
          validUntil: '2024-04-15T23:59:59Z',
          termsAndConditions: [
            'Second lehenga must be of equal or lesser value',
            'Valid on selected designs only',
            'Alteration charges extra',
            'Offer valid for in-store purchases'
          ],
          isActive: true,
          usageCount: 12,
          maxUsage: 25,
          code: 'BOGO2024',
        },
        {
          id: '4',
          title: 'Wedding Photography Package - 20% Off',
          description: 'Professional wedding photography with 20% discount. Capture your special moments.',
          discount: '20% OFF',
          category: 'photography',
          vendorName: 'Perfect Moments Photography',
          vendorId: 'vendor-4',
          image: 'https://images.unsplash.com/photo-1606216794074-735e91aa2c92?w=400',
          originalPrice: 75000,
          discountedPrice: 60000,
          validUntil: '2024-05-31T23:59:59Z',
          termsAndConditions: [
            'Valid for 8-hour photography package',
            'Includes edited photos and album',
            'Advance booking required',
            'Travel charges extra for outstation'
          ],
          isActive: true,
          usageCount: 8,
          maxUsage: 30,
          code: 'PHOTO20',
        },
      ];
      
      setOffers(mockOffers);
    } catch (error) {
      console.error('Error loading offers:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredOffers = offers.filter(offer => {
    const matchesSearch = offer.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         offer.vendorName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         offer.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || offer.category === selectedCategory;
    return matchesSearch && matchesCategory && offer.isActive;
  });

  const handleOfferPress = (offer: Offer) => {
    navigation.navigate('VendorDetail', { vendorId: offer.vendorId });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const isOfferExpiringSoon = (validUntil: string) => {
    const expiryDate = new Date(validUntil);
    const today = new Date();
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 7;
  };

  const renderCategoryFilter = () => (
    <View style={styles.categoryContainer}>
      <FlatList
        data={categories}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.categoryButton,
              selectedCategory === item.id && styles.categoryButtonActive,
              { borderColor: theme.colors.border }
            ]}
            onPress={() => setSelectedCategory(item.id as Offer['category'])}
          >
            <Ionicons 
              name={item.icon as any} 
              size={16} 
              color={selectedCategory === item.id ? theme.colors.primary : theme.colors.textSecondary} 
            />
            <Text
              style={[
                styles.categoryButtonText,
                { color: selectedCategory === item.id ? theme.colors.primary : theme.colors.textSecondary }
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );

  const renderOffer = ({ item }: { item: Offer }) => (
    <Card style={styles.offerCard}>
      <TouchableOpacity onPress={() => handleOfferPress(item)}>
        <View style={styles.offerHeader}>
          <Image source={{ uri: item.image }} style={styles.offerImage} />
          
          <View style={styles.discountBadge}>
            <Text style={styles.discountText}>{item.discount}</Text>
          </View>
          
          {isOfferExpiringSoon(item.validUntil) && (
            <View style={styles.urgencyBadge}>
              <Text style={styles.urgencyText}>Ending Soon!</Text>
            </View>
          )}
        </View>
        
        <View style={styles.offerContent}>
          <Text style={[styles.offerTitle, { color: theme.colors.text }]} numberOfLines={2}>
            {item.title}
          </Text>
          
          <Text style={[styles.vendorName, { color: theme.colors.primary }]}>
            by {item.vendorName}
          </Text>
          
          <Text style={[styles.offerDescription, { color: theme.colors.textSecondary }]} numberOfLines={2}>
            {item.description}
          </Text>
          
          {item.originalPrice && item.discountedPrice && (
            <View style={styles.priceContainer}>
              <Text style={[styles.originalPrice, { color: theme.colors.textSecondary }]}>
                {formatCurrency(item.originalPrice)}
              </Text>
              <Text style={[styles.discountedPrice, { color: theme.colors.success }]}>
                {formatCurrency(item.discountedPrice)}
              </Text>
            </View>
          )}
          
          <View style={styles.offerMeta}>
            <View style={styles.metaItem}>
              <Ionicons name="time" size={14} color={theme.colors.textSecondary} />
              <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
                Valid till {formatDate(item.validUntil)}
              </Text>
            </View>
            
            {item.code && (
              <View style={styles.metaItem}>
                <Ionicons name="pricetag" size={14} color={theme.colors.textSecondary} />
                <Text style={[styles.codeText, { color: theme.colors.primary }]}>
                  Code: {item.code}
                </Text>
              </View>
            )}
          </View>
          
          {item.maxUsage && (
            <View style={styles.usageContainer}>
              <View style={[styles.usageBar, { backgroundColor: theme.colors.border }]}>
                <View 
                  style={[
                    styles.usageProgress, 
                    { 
                      backgroundColor: theme.colors.primary,
                      width: `${(item.usageCount / item.maxUsage) * 100}%`
                    }
                  ]} 
                />
              </View>
              <Text style={[styles.usageText, { color: theme.colors.textSecondary }]}>
                {item.usageCount}/{item.maxUsage} used
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Card>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Special Offers" showBack />
      
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Input
          placeholder="Search offers..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon={<Ionicons name="search" size={20} color={theme.colors.textSecondary} />}
        />
      </View>

      {/* Category Filter */}
      {renderCategoryFilter()}

      {/* Offers List */}
      <FlatList
        data={filteredOffers}
        renderItem={renderOffer}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.offersList}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  categoryContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
    gap: 4,
  },
  categoryButtonActive: {
    backgroundColor: 'rgba(97, 15, 19, 0.1)',
  },
  categoryButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  offersList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  offerCard: {
    marginBottom: 16,
    overflow: 'hidden',
  },
  offerHeader: {
    position: 'relative',
  },
  offerImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  discountBadge: {
    position: 'absolute',
    top: 12,
    left: 12,
    backgroundColor: '#FF4444',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  discountText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  urgencyBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: '#FF8800',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  urgencyText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  offerContent: {
    padding: 16,
  },
  offerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  vendorName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  offerDescription: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 12,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  originalPrice: {
    fontSize: 14,
    textDecorationLine: 'line-through',
  },
  discountedPrice: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  offerMeta: {
    gap: 4,
    marginBottom: 12,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
  },
  codeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  usageContainer: {
    gap: 4,
  },
  usageBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  usageProgress: {
    height: '100%',
    borderRadius: 2,
  },
  usageText: {
    fontSize: 10,
    textAlign: 'right',
  },
});
