import React, { useState, useEffect, useRef } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Image, Dimensions, TextInput, ImageBackground, FlatList, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import { graphqlService } from '../services/graphqlService';
import { Card, CardContent, CardHeader, CardTitle, Button, Input, Badge } from '../components/ui';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppStackParamList } from '../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<AppStackParamList>;

interface FeaturedVendor {
  id: string;
  name: string;
  city: string;
  state: string;
  rating: number;
  profilePhoto?: string;
  category: string;
}

interface ServiceCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  route: string;
}

interface HeroImage {
  id: string;
  uri: string;
  title: string;
}

export default function HomeScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const navigation = useNavigation<NavigationProp>();
  const [featuredVendors, setFeaturedVendors] = useState<FeaturedVendor[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [currentHeroIndex, setCurrentHeroIndex] = useState(0);
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false);
  const [locationInput, setLocationInput] = useState('');

  const { width } = Dimensions.get('window');
  const heroScrollRef = useRef<FlatList>(null);

  // Hero images data
  const heroImages: HeroImage[] = [
    {
      id: '1',
      uri: 'https://images.unsplash.com/photo-1519741497674-611481863552?w=800',
      title: 'Plan Your Dream Wedding With Us!'
    },
    {
      id: '2',
      uri: 'https://images.unsplash.com/photo-1465495976277-4387d4b0e4a6?w=800',
      title: 'Beautiful Wedding Venues'
    },
    {
      id: '3',
      uri: 'https://images.unsplash.com/photo-1511285560929-80b456fea0bc?w=800',
      title: 'Perfect Wedding Services'
    }
  ];

  // Service categories data
  const serviceCategories: ServiceCategory[] = [
    { id: '1', name: 'Venues', icon: 'business', color: theme.colors.primary, route: 'Venues' },
    { id: '2', name: 'Vendors', icon: 'people', color: theme.colors.secondary, route: 'Vendors' },
    { id: '3', name: 'Shop', icon: 'bag', color: theme.colors.accent, route: 'Shop' },
    { id: '4', name: 'Photos', icon: 'camera', color: '#E91E63', route: 'Photos' },
    { id: '5', name: 'Planning', icon: 'calendar', color: '#9C27B0', route: 'Timeline' },
    { id: '6', name: 'Real Weddings', icon: 'heart', color: '#FF5722', route: 'RealWeddings' },
  ];

  useEffect(() => {
    loadFeaturedVendors();
  }, []);

  // Auto-scroll hero images
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentHeroIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % heroImages.length;
        heroScrollRef.current?.scrollToIndex({ index: nextIndex, animated: true });
        return nextIndex;
      });
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  // Handle search functionality
  const handleSearch = () => {
    if (!searchTerm.trim() && !locationInput.trim()) {
      Alert.alert('Search Required', 'Please enter a search term or location');
      return;
    }

    // Determine target screen based on search term
    const searchLower = searchTerm.toLowerCase();
    let targetScreen: keyof AppStackParamList = 'Vendors'; // Default

    if (searchLower.includes('venue') || searchLower.includes('hall') || searchLower.includes('resort')) {
      targetScreen = 'Venues';
    } else if (searchLower.includes('shop') || searchLower.includes('dress') || searchLower.includes('jewelry')) {
      targetScreen = 'Shop';
    }

    // Navigate with search parameters
    const params: any = {};
    if (searchTerm.trim()) params.query = searchTerm;
    if (locationInput.trim()) params.city = locationInput;

    navigation.navigate(targetScreen, params);
  };

  // Handle service category navigation
  const handleServicePress = (route: string) => {
    navigation.navigate(route as keyof AppStackParamList);
  };

  const loadFeaturedVendors = async () => {
    try {
      setLoading(true);
      const result = await graphqlService.listVendors(
        { featured: { eq: true } },
        6 // Limit to 6 featured vendors
      );

      if (result.items) {
        const vendors: FeaturedVendor[] = result.items.map((vendor: any) => ({
          id: vendor.id,
          name: vendor.name,
          city: vendor.city || '',
          state: vendor.state || '',
          rating: vendor.rating || 0,
          profilePhoto: vendor.profilePhoto,
          category: vendor.category || '',
        }));
        setFeaturedVendors(vendors);
      }
    } catch (error) {
      console.error('Error loading featured vendors:', error);
      // Fallback to empty array on error
      setFeaturedVendors([]);
    } finally {
      setLoading(false);
    }
  };

  // Render hero image item
  const renderHeroItem = ({ item }: { item: HeroImage }) => (
    <View style={[styles.heroSlide, { width }]}>
      <ImageBackground
        source={{ uri: item.uri }}
        style={styles.heroImage}
        resizeMode="cover"
      >
        <View style={styles.heroOverlay}>
          <Text style={styles.heroTitle}>{item.title}</Text>
        </View>
      </ImageBackground>
    </View>
  );

  // Render service category item
  const renderServiceCategory = ({ item }: { item: ServiceCategory }) => (
    <TouchableOpacity
      style={[styles.categoryCard, { backgroundColor: item.color }]}
      onPress={() => handleServicePress(item.route)}
    >
      <Ionicons name={item.icon as any} size={32} color="white" />
      <Text style={styles.categoryText}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Hero Section with Background Image */}
      <View style={styles.heroContainer}>
        <ImageBackground
          source={{ uri: 'https://images.unsplash.com/photo-1519741497674-611481863552?w=800' }}
          style={styles.heroBackground}
          resizeMode="cover"
        >
          <View style={styles.heroOverlay}>
            {/* Search Card */}
            <View style={[styles.searchCard, { backgroundColor: 'white' }]}>
              <View style={styles.searchRow}>
                <View style={styles.searchInputContainer}>
                  <Ionicons name="search-outline" size={20} color="#666" />
                  <TextInput
                    style={styles.searchTextInput}
                    placeholder="Search vendors"
                    placeholderTextColor="#666"
                    value={searchTerm}
                    onChangeText={setSearchTerm}
                  />
                </View>
                <View style={styles.searchInputContainer}>
                  <Ionicons name="location-outline" size={20} color="#666" />
                  <TextInput
                    style={styles.searchTextInput}
                    placeholder="Enter city or location"
                    placeholderTextColor="#666"
                    value={locationInput}
                    onChangeText={setLocationInput}
                  />
                </View>
              </View>
              <TouchableOpacity
                style={styles.searchButton}
                onPress={handleSearch}
              >
                <Text style={styles.searchButtonText}>Search</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ImageBackground>
      </View>

      {/* Wedding Services Section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Wedding Services
        </Text>
        <View style={styles.servicesGrid}>
          <TouchableOpacity
            style={[styles.serviceCard, styles.venuesCard]}
            onPress={() => navigation.navigate('Venues')}
          >
            <View style={styles.serviceIconContainer}>
              <Ionicons name="business" size={32} color="white" />
            </View>
            <Text style={styles.serviceCardText}>Venues</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.serviceCard, styles.vendorsCard]}
            onPress={() => navigation.navigate('Vendors')}
          >
            <View style={styles.serviceIconContainer}>
              <Ionicons name="people" size={32} color="white" />
            </View>
            <Text style={styles.serviceCardText}>Vendors</Text>
          </TouchableOpacity>
        </View>
      </View>



      {/* Featured Vendors */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Featured Vendors
        </Text>
        <View style={styles.featuredVendorsGrid}>
          {loading ? (
            // Loading placeholder
            [1, 2].map((item) => (
              <View
                key={item}
                style={[styles.featuredVendorCard, { backgroundColor: theme.colors.surface }]}
              >
                <View style={[styles.featuredVendorImage, { backgroundColor: theme.colors.border }]} />
              </View>
            ))
          ) : featuredVendors.length > 0 ? (
            featuredVendors.slice(0, 2).map((vendor) => (
              <TouchableOpacity
                key={vendor.id}
                style={[styles.featuredVendorCard, { backgroundColor: theme.colors.surface }]}
                onPress={() => navigation.navigate('VendorDetail', { vendorId: vendor.id })}
              >
                {vendor.profilePhoto ? (
                  <Image
                    source={{ uri: vendor.profilePhoto }}
                    style={styles.featuredVendorImage}
                  />
                ) : (
                  <View style={[styles.featuredVendorImage, { backgroundColor: theme.colors.border }]}>
                    <Ionicons name="business-outline" size={40} color={theme.colors.textSecondary} />
                  </View>
                )}
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.featuredVendorCard}>
              <View style={[styles.featuredVendorImage, { backgroundColor: theme.colors.border }]}>
                <Ionicons name="business-outline" size={40} color={theme.colors.textSecondary} />
              </View>
            </View>
          )}
        </View>
      </View>

    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  // Hero Section Styles
  heroContainer: {
    height: 300,
    position: 'relative',
  },
  heroBackground: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  heroOverlay: {
    backgroundColor: 'rgba(0,0,0,0.3)',
    padding: 20,
    paddingBottom: 30,
  },
  // Search Card Styles
  searchCard: {
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  searchRow: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 8,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    gap: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  searchTextInput: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  searchButton: {
    backgroundColor: '#8B1538',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  // Section Styles
  section: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  // Wedding Services Styles
  servicesGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  serviceCard: {
    flex: 1,
    height: 120,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  venuesCard: {
    backgroundColor: '#8B1538',
  },
  vendorsCard: {
    backgroundColor: '#7CB342',
  },
  serviceIconContainer: {
    marginBottom: 8,
  },
  serviceCardText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // Featured Vendors Styles
  featuredVendorsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  featuredVendorCard: {
    flex: 1,
    height: 120,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featuredVendorImage: {
    width: 80,
    height: 80,
    backgroundColor: '#f0f0f0',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
