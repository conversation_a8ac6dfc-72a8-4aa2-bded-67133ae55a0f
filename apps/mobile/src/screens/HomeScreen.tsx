import React, { useState, useEffect, useRef } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Image, Dimensions, TextInput, ImageBackground, FlatList, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import { graphqlService } from '../services/graphqlService';
import { Card, CardContent, CardHeader, CardTitle, Button, Input, Badge } from '../components/ui';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppStackParamList } from '../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<AppStackParamList>;

const serviceButtonClass = "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 w-full md:w-auto";

interface FeaturedVendor {
  id: string;
  name: string;
  city: string;
  state: string;
  rating: number;
  profilePhoto?: string;
  category: string;
}

interface ServiceCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  route: string;
}

interface HeroImage {
  id: string;
  uri: string;
  title: string;
}

export default function HomeScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const navigation = useNavigation<NavigationProp>();
  const [featuredVendors, setFeaturedVendors] = useState<FeaturedVendor[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [currentHeroIndex, setCurrentHeroIndex] = useState(0);
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false);
  const [locationInput, setLocationInput] = useState('');

  // Services carousel state
  const [canScrollServicesLeft, setCanScrollServicesLeft] = useState(false);
  const [canScrollServicesRight, setCanScrollServicesRight] = useState(true);
  const servicesCarouselRef = useRef<ScrollView>(null);

  const { width } = Dimensions.get('window');
  const heroScrollRef = useRef<FlatList>(null);

  // Hero images data
  const heroImages: HeroImage[] = [
    {
      id: '1',
      uri: 'https://images.unsplash.com/photo-1519741497674-611481863552?w=800',
      title: 'Plan Your Dream Wedding With Us!'
    },
    {
      id: '2',
      uri: 'https://images.unsplash.com/photo-1465495976277-4387d4b0e4a6?w=800',
      title: 'Beautiful Wedding Venues'
    },
    {
      id: '3',
      uri: 'https://images.unsplash.com/photo-1511285560929-80b456fea0bc?w=800',
      title: 'Perfect Wedding Services'
    }
  ];

  // Service categories data
  const serviceCategories: ServiceCategory[] = [
    { id: '1', name: 'Venues', icon: 'business', color: theme.colors.primary, route: 'Venues' },
    { id: '2', name: 'Vendors', icon: 'people', color: theme.colors.secondary, route: 'Vendors' },
    { id: '3', name: 'Shop', icon: 'bag', color: theme.colors.accent, route: 'Shop' },
    { id: '4', name: 'Photos', icon: 'camera', color: '#E91E63', route: 'Photos' },
    { id: '5', name: 'Planning', icon: 'calendar', color: '#9C27B0', route: 'Timeline' },
    { id: '6', name: 'Real Weddings', icon: 'heart', color: '#FF5722', route: 'RealWeddings' },
  ];

  useEffect(() => {
    loadFeaturedVendors();
  }, []);

  // Auto-scroll hero images
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentHeroIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % heroImages.length;
        heroScrollRef.current?.scrollToIndex({ index: nextIndex, animated: true });
        return nextIndex;
      });
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  // Services carousel functions
  const checkServicesScrollPosition = () => {
    if (servicesCarouselRef.current) {
      // For React Native ScrollView, we need to handle scroll position differently
      // This is a simplified version - in a real app you'd use onScroll event
      setCanScrollServicesLeft(true); // Simplified for mobile
      setCanScrollServicesRight(true); // Simplified for mobile
    }
  };

  const scrollServicesLeft = () => {
    if (servicesCarouselRef.current) {
      const scrollAmount = 200; // Adjust for mobile card width
      servicesCarouselRef.current.scrollTo({
        x: -scrollAmount,
        animated: true
      });
    }
  };

  const scrollServicesRight = () => {
    if (servicesCarouselRef.current) {
      const scrollAmount = 200; // Adjust for mobile card width
      servicesCarouselRef.current.scrollTo({
        x: scrollAmount,
        animated: true
      });
    }
  };

  // Handle search functionality
  const handleSearch = () => {
    if (!searchTerm.trim() && !locationInput.trim()) {
      Alert.alert('Search Required', 'Please enter a search term or location');
      return;
    }

    // Determine target screen based on search term
    const searchLower = searchTerm.toLowerCase();
    let targetScreen: keyof AppStackParamList = 'Vendors'; // Default

    if (searchLower.includes('venue') || searchLower.includes('hall') || searchLower.includes('resort')) {
      targetScreen = 'Venues';
    } else if (searchLower.includes('shop') || searchLower.includes('dress') || searchLower.includes('jewelry')) {
      targetScreen = 'Shop';
    }

    // Navigate with search parameters
    const params: any = {};
    if (searchTerm.trim()) params.query = searchTerm;
    if (locationInput.trim()) params.city = locationInput;

    navigation.navigate(targetScreen, params);
  };

  // Handle service category navigation
  const handleServicePress = (route: string) => {
    navigation.navigate(route as keyof AppStackParamList);
  };

  const loadFeaturedVendors = async () => {
    try {
      setLoading(true);
      const result = await graphqlService.listVendors(
        { featured: { eq: true } },
        6 // Limit to 6 featured vendors
      );

      if (result.items) {
        const vendors: FeaturedVendor[] = result.items.map((vendor: any) => ({
          id: vendor.id,
          name: vendor.name,
          city: vendor.city || '',
          state: vendor.state || '',
          rating: vendor.rating || 0,
          profilePhoto: vendor.profilePhoto,
          category: vendor.category || '',
        }));
        setFeaturedVendors(vendors);
      }
    } catch (error) {
      console.error('Error loading featured vendors:', error);
      // Fallback to empty array on error
      setFeaturedVendors([]);
    } finally {
      setLoading(false);
    }
  };

  // Render hero image item
  const renderHeroItem = ({ item }: { item: HeroImage }) => (
    <View style={[styles.heroSlide, { width }]}>
      <ImageBackground
        source={{ uri: item.uri }}
        style={styles.heroImage}
        resizeMode="cover"
      >
        <View style={styles.heroOverlay}>
          <Text style={styles.heroTitle}>{item.title}</Text>
        </View>
      </ImageBackground>
    </View>
  );

  // Render service category item
  const renderServiceCategory = ({ item }: { item: ServiceCategory }) => (
    <TouchableOpacity
      style={[styles.categoryCard, { backgroundColor: item.color }]}
      onPress={() => handleServicePress(item.route)}
    >
      <Ionicons name={item.icon as any} size={32} color="white" />
      <Text style={styles.categoryText}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Hero Section with Background Image */}
      <View style={styles.heroContainer}>
        <ImageBackground
          source={{ uri: 'https://images.unsplash.com/photo-1519741497674-611481863552?w=800' }}
          style={styles.heroBackground}
          resizeMode="cover"
        >
          <View style={styles.heroOverlay}>
            {/* Search Card */}
            <View style={[styles.searchCard, { backgroundColor: 'white' }]}>
              <View style={styles.searchRow}>
                <View style={styles.searchInputContainer}>
                  <Ionicons name="search-outline" size={20} color="#666" />
                  <TextInput
                    style={styles.searchTextInput}
                    placeholder="Search vendors"
                    placeholderTextColor="#666"
                    value={searchTerm}
                    onChangeText={setSearchTerm}
                  />
                </View>
                <View style={styles.searchInputContainer}>
                  <Ionicons name="location-outline" size={20} color="#666" />
                  <TextInput
                    style={styles.searchTextInput}
                    placeholder="Enter city or location"
                    placeholderTextColor="#666"
                    value={locationInput}
                    onChangeText={setLocationInput}
                  />
                </View>
              </View>
              <TouchableOpacity
                style={styles.searchButton}
                onPress={handleSearch}
              >
                <Text style={styles.searchButtonText}>Search</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ImageBackground>
      </View>

      {/* Wedding Services Section */}
      <View style={[styles.section, styles.categoriesSection]}>
        {/* Decorative Elements */}
        <View style={styles.decorativeTop} />
        <View style={styles.decorativeBottom} />

        <View style={styles.categoryContainer}>
          <View style={styles.categoryHeader}>
            <View style={styles.categoryIconContainer}>
              <Ionicons name="heart" size={24} color="white" />
            </View>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Popular Wedding Services
            </Text>
          </View>

          <View style={styles.scrollControls}>
            <TouchableOpacity
              style={[
                styles.scrollButton,
                !canScrollServicesLeft && styles.scrollButtonDisabled
              ]}
              onPress={scrollServicesLeft}
              disabled={!canScrollServicesLeft}
            >
              <Ionicons name="chevron-back" size={20} color="#666" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.scrollButton,
                !canScrollServicesRight && styles.scrollButtonDisabled
              ]}
              onPress={scrollServicesRight}
              disabled={!canScrollServicesRight}
            >
              <Ionicons name="chevron-forward" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView
            ref={servicesCarouselRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.servicesCarousel}
            contentContainerStyle={styles.servicesCarouselContent}
          >
            {/* Marriage Mahal */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'marriage-mahal' })}
              >
                <Image
                  source={{ uri: './assets/mahal.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'marriage-mahal' })}
              >
                <Text style={styles.categoryButtonText}>Marriage Mahal</Text>
              </TouchableOpacity>
            </View>

            {/* Photo & Videographers */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'photo-videographers' })}
              >
                <Image
                  source={{ uri: './assets/photographer.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'photo-videographers' })}
              >
                <Text style={styles.categoryButtonText}>Photo & Videographers</Text>
              </TouchableOpacity>
            </View>

            {/* Cooks & Caterings */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'cooks-caterings' })}
              >
                <Image
                  source={{ uri: './assets/catering.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'cooks-caterings' })}
              >
                <Text style={styles.categoryButtonText}>Cooks & Caterings</Text>
              </TouchableOpacity>
            </View>

            {/* Makeup & Mehandi Artists */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'bridal-makeup-artists' })}
              >
                <Image
                  source={{ uri: './assets/bride_makeup.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'bridal-makeup-artists' })}
              >
                <Text style={styles.categoryButtonText}>Makeup & Mehandi Artists</Text>
              </TouchableOpacity>
            </View>

            {/* Musical Artists */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'musical-artists' })}
              >
                <Image
                  source={{ uri: './assets/nadasvaram.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'musical-artists' })}
              >
                <Text style={styles.categoryButtonText}>Musical Artists</Text>
              </TouchableOpacity>
            </View>

            {/* Invitations */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'invitations' })}
              >
                <Image
                  source={{ uri: './assets/invitations.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'invitations' })}
              >
                <Text style={styles.categoryButtonText}>Invitations</Text>
              </TouchableOpacity>
            </View>

            {/* Wedding Jewellery Sets */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'wedding-jewellery-sets' })}
              >
                <Image
                  source={{ uri: './assets/wedding_jewels.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'wedding-jewellery-sets' })}
              >
                <Text style={styles.categoryButtonText}>Wedding Jewellery Sets</Text>
              </TouchableOpacity>
            </View>

            {/* Marriage Outfits */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'marriage-outfits' })}
              >
                <Image
                  source={{ uri: './assets/dress_store.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'marriage-outfits' })}
              >
                <Text style={styles.categoryButtonText}>Marriage Outfits</Text>
              </TouchableOpacity>
            </View>

            {/* Astrologer */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'astrologer' })}
              >
                <Image
                  source={{ uri: './assets/astrologer.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'astrologer' })}
              >
                <Text style={styles.categoryButtonText}>Astrologers</Text>
              </TouchableOpacity>
            </View>

            {/* DJ Music */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'dj-music' })}
              >
                <Image
                  source={{ uri: './assets/dj_music.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'dj-music' })}
              >
                <Text style={styles.categoryButtonText}>DJ Music</Text>
              </TouchableOpacity>
            </View>

            {/* Decorators */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'decorators' })}
              >
                <Image
                  source={{ uri: './assets/decorators.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'decorators' })}
              >
                <Text style={styles.categoryButtonText}>Decorators</Text>
              </TouchableOpacity>
            </View>

            {/* Snacks Stall */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'snacks-shops' })}
              >
                <Image
                  source={{ uri: './assets/snacks_shop.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'snacks-shops' })}
              >
                <Text style={styles.categoryButtonText}>Snacks Stall</Text>
              </TouchableOpacity>
            </View>

            {/* Event Organizers */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'event-organizers' })}
              >
                <Image
                  source={{ uri: './assets/event_organizers.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'event-organizers' })}
              >
                <Text style={styles.categoryButtonText}>Event Organizers</Text>
              </TouchableOpacity>
            </View>

            {/* Iyer/Pandit */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'iyer-pandit' })}
              >
                <Image
                  source={{ uri: './assets/iyer_image.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'iyer-pandit' })}
              >
                <Text style={styles.categoryButtonText}>Iyer/Pandit</Text>
              </TouchableOpacity>
            </View>

            {/* Return Gift */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'return-gift' })}
              >
                <Image
                  source={{ uri: './assets/return_gift.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'return-gift' })}
              >
                <Text style={styles.categoryButtonText}>Return Gift</Text>
              </TouchableOpacity>
            </View>

            {/* Flower Shops */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'flower-shops' })}
              >
                <Image
                  source={{ uri: './assets/flower_shops.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'flower-shops' })}
              >
                <Text style={styles.categoryButtonText}>Flower Shops</Text>
              </TouchableOpacity>
            </View>

            {/* Travels */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'travels' })}
              >
                <Image
                  source={{ uri: './assets/transportations.webp' }}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'travels' })}
              >
                <Text style={styles.categoryButtonText}>Travels</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </View>



      {/* Featured Vendors */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Featured Vendors
        </Text>
        <View style={styles.featuredVendorsGrid}>
          {loading ? (
            // Loading placeholder
            [1, 2].map((item) => (
              <View
                key={item}
                style={[styles.featuredVendorCard, { backgroundColor: theme.colors.surface }]}
              >
                <View style={[styles.featuredVendorImage, { backgroundColor: theme.colors.border }]} />
              </View>
            ))
          ) : featuredVendors.length > 0 ? (
            featuredVendors.slice(0, 2).map((vendor) => (
              <TouchableOpacity
                key={vendor.id}
                style={[styles.featuredVendorCard, { backgroundColor: theme.colors.surface }]}
                onPress={() => navigation.navigate('VendorDetail', { vendorId: vendor.id })}
              >
                {vendor.profilePhoto ? (
                  <Image
                    source={{ uri: vendor.profilePhoto }}
                    style={styles.featuredVendorImage}
                  />
                ) : (
                  <View style={[styles.featuredVendorImage, { backgroundColor: theme.colors.border }]}>
                    <Ionicons name="business-outline" size={40} color={theme.colors.textSecondary} />
                  </View>
                )}
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.featuredVendorCard}>
              <View style={[styles.featuredVendorImage, { backgroundColor: theme.colors.border }]}>
                <Ionicons name="business-outline" size={40} color={theme.colors.textSecondary} />
              </View>
            </View>
          )}
        </View>
      </View>

    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  // Hero Section Styles
  heroContainer: {
    height: 300,
    position: 'relative',
  },
  heroBackground: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  heroOverlay: {
    backgroundColor: 'rgba(0,0,0,0.3)',
    padding: 20,
    paddingBottom: 30,
  },
  // Search Card Styles
  searchCard: {
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  searchRow: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 8,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    gap: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  searchTextInput: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  searchButton: {
    backgroundColor: '#8B1538',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  // Section Styles
  section: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  // Categories Section Styles
  categoriesSection: {
    position: 'relative',
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  decorativeTop: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 128,
    height: 128,
    backgroundColor: 'rgba(139, 21, 56, 0.2)',
    borderRadius: 64,
    transform: [{ translateX: 64 }, { translateY: -64 }],
  },
  decorativeBottom: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: 160,
    height: 160,
    backgroundColor: 'rgba(124, 179, 66, 0.1)',
    borderRadius: 80,
    transform: [{ translateX: -80 }, { translateY: 80 }],
  },
  categoryContainer: {
    position: 'relative',
    paddingHorizontal: 8,
  },
  categoryHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  categoryIconContainer: {
    width: 64,
    height: 64,
    backgroundColor: '#8B1538',
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  scrollControls: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginBottom: 16,
  },
  scrollButton: {
    width: 32,
    height: 32,
    backgroundColor: '#f5f5f5',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  scrollButtonDisabled: {
    opacity: 0.5,
  },
  servicesCarousel: {
    paddingBottom: 8,
  },
  servicesCarouselContent: {
    paddingHorizontal: 8,
    gap: 8,
  },
  categoryCard: {
    width: 176,
    height: 240,
    marginRight: 8,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  categoryImageButton: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  categoryImage: {
    width: '100%',
    height: '100%',
  },
  categoryButton: {
    height: 64,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e5e5e5',
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
  // Featured Vendors Styles
  featuredVendorsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  featuredVendorCard: {
    flex: 1,
    height: 120,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featuredVendorImage: {
    width: 80,
    height: 80,
    backgroundColor: '#f0f0f0',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
