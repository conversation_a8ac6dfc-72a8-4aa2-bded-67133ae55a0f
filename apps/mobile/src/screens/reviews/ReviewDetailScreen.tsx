import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';
import { useAuth } from '../../providers/AuthProvider';
import { Header } from '../../components/Header';
import { Card, Button, Badge } from '../../components/ui';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { RouteProp } from '@react-navigation/native';
import type { AppStackParamList } from '../../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<AppStackParamList>;
type RouteProp = RouteProp<AppStackParamList, 'ReviewDetail'>;

interface Review {
  id: string;
  customerName: string;
  customerAvatar?: string;
  rating: number;
  title: string;
  content: string;
  images: string[];
  vendorName: string;
  vendorId: string;
  serviceType: string;
  eventDate: string;
  createdAt: string;
  isVerified: boolean;
  helpfulCount: number;
  isHelpful: boolean;
  response?: {
    content: string;
    createdAt: string;
    vendorName: string;
  };
}

export default function ReviewDetailScreen() {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp>();
  const { user } = useAuth();
  const [review, setReview] = useState<Review | null>(null);
  const [loading, setLoading] = useState(true);

  const { reviewId } = route.params;

  useEffect(() => {
    loadReviewDetail();
  }, [reviewId]);

  const loadReviewDetail = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockReview: Review = {
        id: reviewId,
        customerName: 'Priya Sharma',
        customerAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
        rating: 5,
        title: 'Absolutely Amazing Wedding Decoration!',
        content: `I cannot express how thrilled I am with the wedding decoration service provided by Elegant Decorations. From the initial consultation to the final setup, everything was perfect.

The team was incredibly professional and understood exactly what we wanted for our dream wedding. They transformed our venue into a magical wonderland that exceeded all our expectations.

The attention to detail was remarkable - from the beautiful floral arrangements to the elegant lighting setup. Our guests couldn't stop complimenting the beautiful decorations throughout the evening.

The team was punctual, organized, and worked seamlessly to ensure everything was ready on time. They even helped with last-minute adjustments without any hassle.

I would highly recommend Elegant Decorations to anyone looking for exceptional wedding decoration services. They truly made our special day unforgettable!`,
        images: [
          'https://images.unsplash.com/photo-1519741497674-611481863552?w=400',
          'https://images.unsplash.com/photo-1465495976277-4387d4b0e4a6?w=400',
          'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=400',
        ],
        vendorName: 'Elegant Decorations',
        vendorId: 'vendor-1',
        serviceType: 'Wedding Decoration',
        eventDate: '2024-01-15',
        createdAt: '2024-01-20T10:30:00Z',
        isVerified: true,
        helpfulCount: 24,
        isHelpful: false,
        response: {
          content: 'Thank you so much for your wonderful review, Priya! It was our pleasure to be part of your special day. We are thrilled that you loved the decorations and that everything exceeded your expectations. Wishing you a lifetime of happiness!',
          createdAt: '2024-01-21T14:15:00Z',
          vendorName: 'Elegant Decorations Team',
        },
      };
      
      setReview(mockReview);
    } catch (error) {
      console.error('Error loading review detail:', error);
      Alert.alert('Error', 'Failed to load review details');
    } finally {
      setLoading(false);
    }
  };

  const handleHelpfulPress = () => {
    if (!review) return;
    
    setReview(prev => prev ? {
      ...prev,
      isHelpful: !prev.isHelpful,
      helpfulCount: prev.isHelpful ? prev.helpfulCount - 1 : prev.helpfulCount + 1,
    } : null);
  };

  const handleVendorPress = () => {
    if (review) {
      navigation.navigate('VendorDetail', { vendorId: review.vendorId });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Ionicons
        key={index}
        name={index < rating ? 'star' : 'star-outline'}
        size={20}
        color="#FFD700"
      />
    ));
  };

  if (loading || !review) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header title="Review Details" showBack />
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
            Loading review...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Review Details" showBack />
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Review Header */}
        <Card style={styles.reviewCard}>
          <View style={styles.reviewHeader}>
            <View style={styles.customerInfo}>
              {review.customerAvatar ? (
                <Image source={{ uri: review.customerAvatar }} style={styles.customerAvatar} />
              ) : (
                <View style={[styles.customerAvatarPlaceholder, { backgroundColor: theme.colors.border }]}>
                  <Ionicons name="person" size={24} color={theme.colors.textSecondary} />
                </View>
              )}
              
              <View style={styles.customerDetails}>
                <View style={styles.customerNameRow}>
                  <Text style={[styles.customerName, { color: theme.colors.text }]}>
                    {review.customerName}
                  </Text>
                  {review.isVerified && (
                    <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
                  )}
                </View>
                <Text style={[styles.reviewDate, { color: theme.colors.textSecondary }]}>
                  {formatDate(review.createdAt)}
                </Text>
              </View>
            </View>
            
            <View style={styles.ratingContainer}>
              <View style={styles.stars}>
                {renderStars(review.rating)}
              </View>
              <Text style={[styles.ratingText, { color: theme.colors.text }]}>
                {review.rating}/5
              </Text>
            </View>
          </View>
          
          <Text style={[styles.reviewTitle, { color: theme.colors.text }]}>
            {review.title}
          </Text>
          
          <View style={styles.serviceInfo}>
            <TouchableOpacity onPress={handleVendorPress} style={styles.vendorButton}>
              <Text style={[styles.vendorName, { color: theme.colors.primary }]}>
                {review.vendorName}
              </Text>
            </TouchableOpacity>
            <Text style={[styles.serviceType, { color: theme.colors.textSecondary }]}>
              {review.serviceType}
            </Text>
            <Text style={[styles.eventDate, { color: theme.colors.textSecondary }]}>
              Event Date: {formatDate(review.eventDate)}
            </Text>
          </View>
        </Card>

        {/* Review Content */}
        <Card style={styles.contentCard}>
          <Text style={[styles.reviewContent, { color: theme.colors.text }]}>
            {review.content}
          </Text>
        </Card>

        {/* Review Images */}
        {review.images.length > 0 && (
          <Card style={styles.imagesCard}>
            <Text style={[styles.imagesTitle, { color: theme.colors.text }]}>
              Photos
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imagesContainer}>
              {review.images.map((image, index) => (
                <Image key={index} source={{ uri: image }} style={styles.reviewImage} />
              ))}
            </ScrollView>
          </Card>
        )}

        {/* Vendor Response */}
        {review.response && (
          <Card style={styles.responseCard}>
            <View style={styles.responseHeader}>
              <Ionicons name="chatbubble" size={20} color={theme.colors.primary} />
              <Text style={[styles.responseTitle, { color: theme.colors.primary }]}>
                Response from {review.response.vendorName}
              </Text>
            </View>
            <Text style={[styles.responseContent, { color: theme.colors.text }]}>
              {review.response.content}
            </Text>
            <Text style={[styles.responseDate, { color: theme.colors.textSecondary }]}>
              {formatDate(review.response.createdAt)}
            </Text>
          </Card>
        )}

        {/* Helpful Actions */}
        <Card style={styles.actionsCard}>
          <Text style={[styles.actionsTitle, { color: theme.colors.text }]}>
            Was this review helpful?
          </Text>
          
          <View style={styles.helpfulContainer}>
            <TouchableOpacity
              style={[
                styles.helpfulButton,
                review.isHelpful && styles.helpfulButtonActive,
                { borderColor: theme.colors.border }
              ]}
              onPress={handleHelpfulPress}
            >
              <Ionicons 
                name={review.isHelpful ? "thumbs-up" : "thumbs-up-outline"} 
                size={20} 
                color={review.isHelpful ? theme.colors.primary : theme.colors.textSecondary} 
              />
              <Text style={[
                styles.helpfulButtonText,
                { color: review.isHelpful ? theme.colors.primary : theme.colors.textSecondary }
              ]}>
                Helpful ({review.helpfulCount})
              </Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            onPress={handleVendorPress}
            style={styles.viewVendorButton}
          >
            <Text style={styles.buttonText}>View Vendor Profile</Text>
          </Button>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  reviewCard: {
    marginVertical: 16,
    padding: 16,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  customerInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  customerAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  customerAvatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  customerDetails: {
    flex: 1,
  },
  customerNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 2,
  },
  customerName: {
    fontSize: 16,
    fontWeight: '600',
  },
  reviewDate: {
    fontSize: 12,
  },
  ratingContainer: {
    alignItems: 'flex-end',
  },
  stars: {
    flexDirection: 'row',
    marginBottom: 2,
  },
  ratingText: {
    fontSize: 12,
    fontWeight: '500',
  },
  reviewTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  serviceInfo: {
    gap: 2,
  },
  vendorButton: {
    alignSelf: 'flex-start',
  },
  vendorName: {
    fontSize: 14,
    fontWeight: '500',
  },
  serviceType: {
    fontSize: 12,
  },
  eventDate: {
    fontSize: 12,
  },
  contentCard: {
    marginBottom: 16,
    padding: 16,
  },
  reviewContent: {
    fontSize: 15,
    lineHeight: 22,
  },
  imagesCard: {
    marginBottom: 16,
    padding: 16,
  },
  imagesTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  imagesContainer: {
    flexDirection: 'row',
  },
  reviewImage: {
    width: 120,
    height: 120,
    borderRadius: 8,
    marginRight: 8,
  },
  responseCard: {
    marginBottom: 16,
    padding: 16,
    backgroundColor: 'rgba(97, 15, 19, 0.05)',
  },
  responseHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  responseTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  responseContent: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  responseDate: {
    fontSize: 11,
  },
  actionsCard: {
    marginBottom: 16,
    padding: 16,
  },
  actionsTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  helpfulContainer: {
    flexDirection: 'row',
  },
  helpfulButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  helpfulButtonActive: {
    backgroundColor: 'rgba(97, 15, 19, 0.1)',
  },
  helpfulButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  actionButtons: {
    marginBottom: 32,
  },
  viewVendorButton: {
    paddingVertical: 12,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});
