import React, { useState } from 'react';
import { View, FlatList } from 'react-native';
import { Card, Button, Input, Badge } from '../../components/ui';

const ChecklistScreen = () => {
  const [tasks, setTasks] = useState([
    { id: '1', text: 'Book venue', done: false },
    { id: '2', text: 'Send invitations', done: false },
  ]);
  const [input, setInput] = useState('');

  const addTask = () => {
    if (input.trim()) {
      setTasks([...tasks, { id: Date.now().toString(), text: input, done: false }]);
      setInput('');
    }
  };
  const toggleTask = (id: string) => {
    setTasks(tasks.map(t => t.id === id ? { ...t, done: !t.done } : t));
  };
  const deleteTask = (id: string) => {
    setTasks(tasks.filter(t => t.id !== id));
  };

  return (
    <View style={{ flex: 1, padding: 16 }}>
      <Input
        placeholder="Add a new task..."
        value={input}
        onChangeText={setInput}
        right={<Button onPress={addTask} title="Add" />}
      />
      <FlatList
        data={tasks}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <Card style={{ marginVertical: 8 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
              <Button
                title={item.done ? '✔' : '○'}
                onPress={() => toggleTask(item.id)}
                variant={item.done ? 'success' : 'default'}
              />
              <Badge>{item.text}</Badge>
              <Button title="Delete" onPress={() => deleteTask(item.id)} variant="destructive" />
            </View>
          </Card>
        )}
      />
    </View>
  );
};

export default ChecklistScreen; 