import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';
import { Header } from '../../components/Header';
import { Card, Button, Input } from '../../components/ui';

interface TimelineEvent {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  category: 'ceremony' | 'preparation' | 'vendor' | 'documentation' | 'other';
  isCompleted: boolean;
  priority: 'high' | 'medium' | 'low';
  reminders: string[];
}

interface TimelineCategory {
  id: string;
  name: string;
  color: string;
  icon: string;
}

export default function TimelineScreen() {
  const { theme } = useTheme();
  const [events, setEvents] = useState<TimelineEvent[]>([]);
  const [categories, setCategories] = useState<TimelineCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTimeline();
    loadCategories();
  }, []);

  const loadTimeline = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockEvents: TimelineEvent[] = [
        {
          id: '1',
          title: 'Book Wedding Venue',
          description: 'Finalize and book the main wedding venue with advance payment',
          date: '2024-02-15',
          time: '10:00 AM',
          category: 'vendor',
          isCompleted: true,
          priority: 'high',
          reminders: ['1 week before', '1 day before']
        },
        {
          id: '2',
          title: 'Engagement Ceremony',
          description: 'Traditional engagement ceremony with family and close friends',
          date: '2024-03-01',
          time: '6:00 PM',
          category: 'ceremony',
          isCompleted: true,
          priority: 'high',
          reminders: ['2 weeks before', '1 week before', '1 day before']
        },
        {
          id: '3',
          title: 'Wedding Photography Session',
          description: 'Pre-wedding photoshoot with photographer',
          date: '2024-03-15',
          time: '9:00 AM',
          category: 'preparation',
          isCompleted: false,
          priority: 'medium',
          reminders: ['1 week before', '2 days before']
        },
        {
          id: '4',
          title: 'Mehendi Ceremony',
          description: 'Traditional mehendi ceremony for bride and female relatives',
          date: '2024-04-10',
          time: '2:00 PM',
          category: 'ceremony',
          isCompleted: false,
          priority: 'high',
          reminders: ['1 week before', '1 day before']
        },
        {
          id: '5',
          title: 'Marriage Registration',
          description: 'Legal marriage registration at registrar office',
          date: '2024-04-12',
          time: '11:00 AM',
          category: 'documentation',
          isCompleted: false,
          priority: 'high',
          reminders: ['2 weeks before', '1 week before', '1 day before']
        },
        {
          id: '6',
          title: 'Wedding Ceremony',
          description: 'Main wedding ceremony with all rituals and celebrations',
          date: '2024-04-15',
          time: '7:00 AM',
          category: 'ceremony',
          isCompleted: false,
          priority: 'high',
          reminders: ['1 month before', '1 week before', '1 day before']
        }
      ];

      setEvents(mockEvents);
    } catch (error) {
      console.error('Error loading timeline:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      // TODO: Replace with actual API call
      const mockCategories: TimelineCategory[] = [
        { id: 'all', name: 'All Events', color: theme.colors.primary, icon: 'calendar' },
        { id: 'ceremony', name: 'Ceremonies', color: '#FF6B6B', icon: 'flower' },
        { id: 'preparation', name: 'Preparation', color: '#4ECDC4', icon: 'camera' },
        { id: 'vendor', name: 'Vendors', color: '#45B7D1', icon: 'people' },
        { id: 'documentation', name: 'Documents', color: '#96CEB4', icon: 'document-text' },
        { id: 'other', name: 'Other', color: '#FFEAA7', icon: 'ellipsis-horizontal' }
      ];

      setCategories(mockCategories);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const filteredEvents = selectedCategory === 'all'
    ? events
    : events.filter(event => event.category === selectedCategory);

  const handleToggleComplete = (eventId: string) => {
    setEvents(prevEvents =>
      prevEvents.map(event =>
        event.id === eventId
          ? { ...event, isCompleted: !event.isCompleted }
          : event
      )
    );
  };

  const handleAddEvent = () => {
    // TODO: Navigate to add event screen or show modal
    Alert.alert('Add Event', 'Add new timeline event functionality coming soon!');
  };

  const handleEditEvent = (eventId: string) => {
    // TODO: Navigate to edit event screen
    Alert.alert('Edit Event', 'Edit event functionality coming soon!');
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return theme.colors.destructive;
      case 'medium': return theme.colors.accent;
      case 'low': return theme.colors.secondary;
      default: return theme.colors.textSecondary;
    }
  };

  const getCategoryIcon = (category: string) => {
    const categoryData = categories.find(cat => cat.id === category);
    return categoryData?.icon || 'calendar';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const renderEvent = ({ item }: { item: TimelineEvent }) => (
    <Card style={[styles.eventCard, item.isCompleted && styles.completedCard]}>
      <View style={styles.eventHeader}>
        <View style={styles.eventTitleRow}>
          <TouchableOpacity
            style={styles.checkboxContainer}
            onPress={() => handleToggleComplete(item.id)}
          >
            <Ionicons
              name={item.isCompleted ? "checkmark-circle" : "ellipse-outline"}
              size={24}
              color={item.isCompleted ? theme.colors.secondary : theme.colors.border}
            />
          </TouchableOpacity>

          <View style={styles.eventInfo}>
            <Text style={[
              styles.eventTitle,
              { color: theme.colors.text },
              item.isCompleted && styles.completedText
            ]}>
              {item.title}
            </Text>
            <Text style={[styles.eventDescription, { color: theme.colors.textSecondary }]}>
              {item.description}
            </Text>
          </View>

          <TouchableOpacity
            style={styles.editButton}
            onPress={() => handleEditEvent(item.id)}
          >
            <Ionicons name="pencil" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>

        <View style={styles.eventMeta}>
          <View style={styles.dateTimeContainer}>
            <Ionicons name="calendar" size={16} color={theme.colors.textSecondary} />
            <Text style={[styles.dateText, { color: theme.colors.textSecondary }]}>
              {formatDate(item.date)}
            </Text>
            <Ionicons name="time" size={16} color={theme.colors.textSecondary} />
            <Text style={[styles.timeText, { color: theme.colors.textSecondary }]}>
              {item.time}
            </Text>
          </View>

          <View style={styles.categoryPriorityContainer}>
            <View style={[styles.categoryBadge, { backgroundColor: categories.find(cat => cat.id === item.category)?.color || theme.colors.surface }]}>
              <Ionicons name={getCategoryIcon(item.category) as any} size={12} color="white" />
              <Text style={styles.categoryText}>
                {categories.find(cat => cat.id === item.category)?.name || item.category}
              </Text>
            </View>

            <View style={[styles.priorityBadge, { borderColor: getPriorityColor(item.priority) }]}>
              <Text style={[styles.priorityText, { color: getPriorityColor(item.priority) }]}>
                {item.priority.toUpperCase()}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </Card>
  );

  const renderCategory = ({ item }: { item: TimelineCategory }) => (
    <TouchableOpacity
      style={[
        styles.categoryButton,
        {
          backgroundColor: selectedCategory === item.id ? item.color : theme.colors.surface,
          borderColor: item.color
        }
      ]}
      onPress={() => setSelectedCategory(item.id)}
    >
      <Ionicons
        name={item.icon as any}
        size={16}
        color={selectedCategory === item.id ? 'white' : item.color}
      />
      <Text style={[
        styles.categoryButtonText,
        {
          color: selectedCategory === item.id ? 'white' : item.color
        }
      ]}>
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Wedding Timeline" showBack />

      {/* Categories Filter */}
      <View style={styles.categoriesContainer}>
        <FlatList
          data={categories}
          renderItem={renderCategory}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesList}
        />
      </View>

      {/* Timeline Events */}
      <FlatList
        data={filteredEvents}
        renderItem={renderEvent}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.eventsList}
        showsVerticalScrollIndicator={false}
      />

      {/* Add Event Button */}
      <View style={styles.addButtonContainer}>
        <Button onPress={handleAddEvent} style={styles.addButton}>
          <Ionicons name="add" size={20} color="white" />
          <Text style={styles.addButtonText}>Add Event</Text>
        </Button>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  categoriesContainer: {
    paddingVertical: 12,
  },
  categoriesList: {
    paddingHorizontal: 16,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 12,
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  eventsList: {
    paddingHorizontal: 16,
    paddingBottom: 100,
  },
  eventCard: {
    marginBottom: 16,
    padding: 16,
  },
  completedCard: {
    opacity: 0.7,
  },
  eventHeader: {
    flex: 1,
  },
  eventTitleRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  checkboxContainer: {
    marginRight: 12,
    marginTop: 2,
  },
  eventInfo: {
    flex: 1,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  completedText: {
    textDecorationLine: 'line-through',
  },
  eventDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  editButton: {
    padding: 4,
  },
  eventMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dateText: {
    fontSize: 12,
    marginLeft: 4,
    marginRight: 12,
  },
  timeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  categoryPriorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  categoryText: {
    fontSize: 10,
    color: 'white',
    marginLeft: 4,
    fontWeight: '500',
  },
  priorityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    borderWidth: 1,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
  },
  addButtonContainer: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
  },
  addButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 8,
  },
});
