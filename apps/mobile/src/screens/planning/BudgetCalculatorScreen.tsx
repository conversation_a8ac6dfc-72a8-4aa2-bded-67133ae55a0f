import React, { useState } from 'react';
import { View, FlatList, Text } from 'react-native';
import { Card, Button, Input } from '../../components/ui';

const BudgetCalculatorScreen = () => {
  const [items, setItems] = useState([
    { id: '1', label: 'Venue', amount: 50000 },
    { id: '2', label: 'Catering', amount: 30000 },
  ]);
  const [label, setLabel] = useState('');
  const [amount, setAmount] = useState('');

  const addItem = () => {
    if (label.trim() && amount) {
      setItems([...items, { id: Date.now().toString(), label, amount: parseFloat(amount) }]);
      setLabel('');
      setAmount('');
    }
  };
  const deleteItem = (id: string) => {
    setItems(items.filter(i => i.id !== id));
  };
  const total = items.reduce((sum, i) => sum + i.amount, 0);

  return (
    <View style={{ flex: 1, padding: 16 }}>
      <View style={{ flexDirection: 'row', marginBottom: 16 }}>
        <Input
          placeholder="Item"
          value={label}
          onChangeText={setLabel}
          style={{ flex: 1, marginRight: 8 }}
        />
        <Input
          placeholder="Amount"
          value={amount}
          onChangeText={setAmount}
          keyboardType="numeric"
          style={{ width: 100, marginRight: 8 }}
        />
        <Button title="Add" onPress={addItem} />
      </View>
      <FlatList
        data={items}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <Card style={{ marginVertical: 8 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
              <Text>{item.label}: ₹{item.amount}</Text>
              <Button title="Delete" onPress={() => deleteItem(item.id)} variant="destructive" />
            </View>
          </Card>
        )}
      />
      <Card style={{ marginTop: 16, padding: 16 }}>
        <Text style={{ fontWeight: 'bold', fontSize: 18 }}>Total: ₹{total}</Text>
      </Card>
    </View>
  );
};

export default BudgetCalculatorScreen; 