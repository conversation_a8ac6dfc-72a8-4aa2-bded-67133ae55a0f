import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  Share,
  Dimensions
} from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';
import { Header } from '../../components/Header';
import { Button, Card } from '../../components/ui';
import type { AppStackParamList } from '../../navigation/AppNavigator';

type BlogDetailRouteProp = RouteProp<AppStackParamList, 'BlogDetail'>;

const { width: screenWidth } = Dimensions.get('window');

interface BlogPost {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  author: string;
  publishedAt: string;
  readTime: string;
  category: string;
  tags: string[];
  imageUrl: string;
  likes: number;
  comments: number;
  isLiked: boolean;
}

export default function BlogDetailScreen() {
  const { theme } = useTheme();
  const route = useRoute<BlogDetailRouteProp>();
  const { blogId } = route.params;

  const [blog, setBlog] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [isLiked, setIsLiked] = useState(false);

  useEffect(() => {
    loadBlogDetail();
  }, [blogId]);

  const loadBlogDetail = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockBlog: BlogPost = {
        id: blogId,
        title: "10 Essential Wedding Planning Tips for Indian Weddings",
        content: `Planning an Indian wedding can be both exciting and overwhelming. With multiple ceremonies, hundreds of guests, and countless traditions to honor, it's important to start early and stay organized.

Start Planning Early: Indian weddings typically require 6-12 months of planning. Start with the big decisions like venue and date, then work your way down to the details.

Set a Realistic Budget: Wedding costs can quickly spiral out of control. Set a budget early and stick to it. Consider all ceremonies, not just the main wedding day.

Choose the Right Venue: Your venue sets the tone for your entire celebration. Consider capacity, location, catering options, and decoration possibilities.

Plan Your Guest List: Indian weddings often have large guest lists. Start with immediate family and close friends, then expand as your budget allows.

Book Vendors Early: The best photographers, decorators, and caterers book up quickly, especially during wedding season. Start your vendor search early.

Consider the Weather: Plan your outdoor ceremonies with weather in mind. Have backup plans for rain or extreme heat.

Coordinate Outfits: With multiple ceremonies, you'll need several outfits. Plan these early and consider comfort as well as style.

Plan Your Menu: Food is central to Indian celebrations. Work with your caterer to create a menu that represents your heritage and satisfies your guests.

Don't Forget the Details: Small touches like welcome bags, ceremony programs, and personalized favors make your wedding memorable.

Enjoy the Process: Remember that your wedding is a celebration of love. Don't get so caught up in the details that you forget to enjoy the journey.

Planning a wedding is a marathon, not a sprint. Take time to enjoy each moment and celebrate the milestones along the way.`,
        excerpt: "Essential tips and advice for planning the perfect Indian wedding celebration",
        author: "Priya Sharma",
        publishedAt: "2024-01-15",
        readTime: "8 min read",
        category: "Wedding Planning",
        tags: ["Planning", "Indian Wedding", "Tips", "Advice"],
        imageUrl: "https://images.unsplash.com/photo-1519741497674-************?w=800",
        likes: 245,
        comments: 32,
        isLiked: false
      };

      setBlog(mockBlog);
      setIsLiked(mockBlog.isLiked);
    } catch (error) {
      console.error('Error loading blog detail:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLike = () => {
    setIsLiked(!isLiked);
    // TODO: Update like status via API
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Check out this article: ${blog?.title}`,
        url: `https://thirumanam360.com/blog/${blogId}`,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const handleComment = () => {
    // TODO: Navigate to comments screen or show comment modal
  };

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header title="Loading..." showBack />
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
            Loading article...
          </Text>
        </View>
      </View>
    );
  }

  if (!blog) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header title="Article Not Found" showBack />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.colors.text }]}>
            Article not found
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Article" showBack />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Hero Image */}
        <Image source={{ uri: blog.imageUrl }} style={styles.heroImage} />

        {/* Article Header */}
        <View style={styles.articleHeader}>
          <View style={styles.categoryContainer}>
            <Text style={[styles.category, { color: theme.colors.primary }]}>
              {blog.category}
            </Text>
          </View>

          <Text style={[styles.title, { color: theme.colors.text }]}>
            {blog.title}
          </Text>

          <Text style={[styles.excerpt, { color: theme.colors.textSecondary }]}>
            {blog.excerpt}
          </Text>

          <View style={styles.metaInfo}>
            <Text style={[styles.author, { color: theme.colors.text }]}>
              By {blog.author}
            </Text>
            <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
              {blog.publishedAt} • {blog.readTime}
            </Text>
          </View>
        </View>

        {/* Article Content */}
        <View style={styles.contentContainer}>
          <Text style={[styles.content, { color: theme.colors.text }]}>
            {blog.content}
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
  },
  heroImage: {
    width: screenWidth,
    height: 250,
    resizeMode: 'cover',
  },
  articleHeader: {
    padding: 20,
  },
  categoryContainer: {
    marginBottom: 12,
  },
  category: {
    fontSize: 14,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    lineHeight: 36,
    marginBottom: 12,
  },
  excerpt: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 16,
  },
  metaInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  author: {
    fontSize: 14,
    fontWeight: '600',
  },
  metaText: {
    fontSize: 14,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  content: {
    fontSize: 16,
    lineHeight: 26,
  },
});
