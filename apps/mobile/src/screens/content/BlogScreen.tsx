import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../providers/ThemeProvider';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';
import { Button } from '../../components/ui/Button';

export default function BlogScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [blogs, setBlogs] = useState([
    {
      id: '1',
      title: '10 Essential Wedding Planning Tips for Indian Weddings',
      excerpt: 'Planning an Indian wedding can be overwhelming. Here are the essential tips to make your special day perfect...',
      author: '<PERSON><PERSON>',
      date: '2024-02-15',
      readTime: '5 min read',
      image: 'https://images.unsplash.com/photo-1519741497674-************?w=400',
      category: 'Planning',
      likes: 245,
    },
    {
      id: '2',
      title: 'Traditional vs Modern: Choosing Your Wedding Style',
      excerpt: 'Discover how to blend traditional customs with modern elements for a unique wedding celebration...',
      author: 'Rajesh Kumar',
      date: '2024-02-12',
      readTime: '7 min read',
      image: 'https://images.unsplash.com/photo-*************-a08af7148866?w=400',
      category: 'Style',
      likes: 189,
    },
    {
      id: '3',
      title: 'Budget-Friendly Wedding Decoration Ideas',
      excerpt: 'Create stunning wedding decorations without breaking the bank. Creative ideas for every budget...',
      author: 'Meera Patel',
      date: '2024-02-10',
      readTime: '6 min read',
      image: 'https://images.unsplash.com/photo-*************-7168b8af9bc3?w=400',
      category: 'Decoration',
      likes: 312,
    },
  ]);

  const onRefresh = async () => {
    setRefreshing(true);
    // TODO: Fetch latest blogs
    setTimeout(() => setRefreshing(false), 1000);
  };

  const filteredBlogs = blogs.filter(blog =>
    blog.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    blog.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: 16,
      gap: 16,
    },
    header: {
      marginBottom: 8,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      marginBottom: 16,
    },
    searchContainer: {
      marginBottom: 8,
    },
    blogCard: {
      padding: 0,
      overflow: 'hidden',
      marginBottom: 16,
    },
    blogImage: {
      width: '100%',
      height: 200,
      backgroundColor: theme.colors.muted,
    },
    blogContent: {
      padding: 16,
    },
    blogHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 8,
    },
    categoryBadge: {
      backgroundColor: theme.colors.primary + '20',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    categoryText: {
      fontSize: 12,
      color: theme.colors.primary,
      fontWeight: '500',
    },
    blogTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
      lineHeight: 24,
    },
    blogExcerpt: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 20,
      marginBottom: 12,
    },
    blogMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    metaLeft: {
      flex: 1,
    },
    authorText: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: 2,
    },
    dateText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    metaRight: {
      alignItems: 'flex-end',
    },
    readTimeText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 2,
    },
    likesContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    likesText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    emptyIcon: {
      fontSize: 64,
      color: theme.colors.textSecondary,
      marginBottom: 16,
    },
    emptyTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    emptyText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.header}>
          <Text style={styles.title}>Wedding Blog</Text>
          <Text style={styles.subtitle}>
            Tips, ideas, and inspiration for your perfect wedding
          </Text>
        </View>

        <View style={styles.searchContainer}>
          <Input
            placeholder="Search articles..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        {filteredBlogs.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyIcon}>📝</Text>
            <Text style={styles.emptyTitle}>No Articles Found</Text>
            <Text style={styles.emptyText}>
              Try adjusting your search terms
            </Text>
          </View>
        ) : (
          filteredBlogs.map((blog) => (
            <Card
              key={blog.id}
              style={styles.blogCard}
              onPress={() =>
                navigation.navigate('BlogDetail' as never, { blogId: blog.id })
              }
            >
              <Image
                source={{ uri: blog.image }}
                style={styles.blogImage}
                resizeMode="cover"
              />
              <View style={styles.blogContent}>
                <View style={styles.blogHeader}>
                  <View style={styles.categoryBadge}>
                    <Text style={styles.categoryText}>{blog.category}</Text>
                  </View>
                </View>
                
                <Text style={styles.blogTitle}>{blog.title}</Text>
                <Text style={styles.blogExcerpt}>{blog.excerpt}</Text>
                
                <View style={styles.blogMeta}>
                  <View style={styles.metaLeft}>
                    <Text style={styles.authorText}>{blog.author}</Text>
                    <Text style={styles.dateText}>{blog.date}</Text>
                  </View>
                  <View style={styles.metaRight}>
                    <Text style={styles.readTimeText}>{blog.readTime}</Text>
                    <View style={styles.likesContainer}>
                      <Text style={styles.likesText}>❤️ {blog.likes}</Text>
                    </View>
                  </View>
                </View>
              </View>
            </Card>
          ))
        )}
      </ScrollView>
    </View>
  );
}
