import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Image, FlatList, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';

interface Venue {
  id: string;
  name: string;
  description: string;
  address: string;
  city: string;
  state: string;
  contact: string;
  email: string;
  website?: string;
  venueType: string;
  capacity: { min: number; max: number };
  priceRange: string;
  amenities: string[];
  gallery: string[];
  rating: number;
  reviewCount: number;
  verified: boolean;
  featured: boolean;
  policies: string[];
  catering: boolean;
  decoration: boolean;
  parking: boolean;
  accommodation: boolean;
}

interface Props {
  navigation: any;
  route: any;
}

const RenderImageWithFallback = ({ uri, style }: { uri?: string; style?: any }) => {
  if (uri) {
    return <Image source={{ uri }} style={style} resizeMode="cover" />;
  }
  return (
    <View style={[style, { backgroundColor: '#E5E7EB', justifyContent: 'center', alignItems: 'center' }]}>
      <Text style={{ color: '#9CA3AF', fontSize: 16 }}>No Image</Text>
    </View>
  );
};

export default function VenueDetailScreen({ navigation, route }: Props) {
  const { theme } = useTheme();
  const { venueId } = route.params;
  const [venue, setVenue] = useState<Venue | null>(null);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);
  const [activeTab, setActiveTab] = useState<'Overview' | 'Gallery' | 'Reviews'>('Overview');

  useEffect(() => {
    loadVenueDetails();
  }, [venueId]);

  const loadVenueDetails = async () => {
    // Replace with your API call
    const mockVenue: Venue = {
      id: venueId,
      name: 'Grand Palace Wedding Hall',
      description: 'Elegant and spacious wedding hall perfect for grand celebrations. Our venue combines traditional architecture with modern amenities to create the perfect setting for your special day.',
      address: '456 Wedding Avenue, Anna Nagar',
      city: 'Chennai',
      state: 'Tamil Nadu',
      contact: '+91 98765 43211',
      email: '<EMAIL>',
      website: 'https://grandpalace.com',
      venueType: 'Banquet Hall',
      capacity: { min: 100, max: 1000 },
      priceRange: '₹50,000 - ₹2,00,000',
      amenities: [
        'Air Conditioning', 'Sound System', 'Stage Setup', 'Bridal Room',
        'Parking', 'Catering Kitchen', 'Generator Backup', 'Security',
      ],
      gallery: [
        'https://images.unsplash.com/photo-1519167758481-83f29c7c8dc8?w=300',
        'https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?w=300',
        'https://images.unsplash.com/photo-1470229722913-7c0e2dbbafd3?w=300',
      ],
      rating: 4.6,
      reviewCount: 89,
      verified: true,
      featured: true,
      policies: [
        'No outside alcohol allowed',
        'Decoration setup 4 hours before event',
        'Music must stop by 11 PM',
        'Security deposit required',
      ],
      catering: true,
      decoration: true,
      parking: true,
      accommodation: false,
    };
    setVenue(mockVenue);
    setLoading(false);
  };

  const handleCall = () => venue?.contact && Alert.alert('Call', venue.contact);
  const handleEmail = () => venue?.email && Alert.alert('Email', venue.email);
  const handleWebsite = () => venue?.website && Alert.alert('Website', venue.website);
  const toggleFavorite = () => setIsFavorite(!isFavorite);
  const handleBooking = () => Alert.alert('Booking', 'Booking functionality will be implemented soon');
  const handleWriteReview = () => Alert.alert('Review', 'Review functionality coming soon!');
  const handleInquiry = () => Alert.alert('Inquiry', 'Inquiry submitted!');

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading...</Text>
      </View>
    );
  }
  if (!venue) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.errorText, { color: theme.colors.error }]}>Venue not found</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
      {/* Hero Section */}
      <View style={styles.heroContainer}>
        <RenderImageWithFallback uri={venue.gallery[0]} style={styles.heroImage} />
        <View style={styles.heroOverlay}>
          <Text style={styles.heroName}>{venue.name}</Text>
          <Text style={styles.heroPrice}>{venue.priceRange}</Text>
          <Text style={styles.heroCategory}>{venue.venueType}</Text>
          <View style={styles.heroLocationRow}>
            <Ionicons name="location-outline" size={16} color="#fff" />
            <Text style={styles.heroLocation}>{venue.city}, {venue.state}</Text>
          </View>
          <View style={styles.heroRatingRow}>
            <Ionicons name="star" size={16} color="#FFD700" />
            <Text style={styles.heroRating}>{venue.rating}</Text>
          </View>
        </View>
        <TouchableOpacity style={styles.favoriteButton} onPress={toggleFavorite}>
          <Ionicons name={isFavorite ? 'heart' : 'heart-outline'} size={24} color={isFavorite ? '#EF4444' : '#fff'} />
        </TouchableOpacity>
      </View>

      {/* Tab Bar */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-around', marginVertical: 12 }}>
        {['Overview', 'Gallery', 'Reviews'].map(tab => (
          <TouchableOpacity
            key={tab}
            onPress={() => setActiveTab(tab as any)}
            style={{
              borderBottomWidth: activeTab === tab ? 2 : 0,
              borderBottomColor: activeTab === tab ? theme.colors.primary : 'transparent',
              paddingVertical: 8,
              flex: 1,
              alignItems: 'center',
            }}
          >
            <Text style={{ color: activeTab === tab ? theme.colors.primary : theme.colors.textSecondary, fontWeight: 'bold' }}>{tab}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Tab Content */}
      <ScrollView style={{ flex: 1 }}>
        {activeTab === 'Overview' && (
          <View>
            {/* Info Card */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface, marginTop: -40, marginHorizontal: 16, zIndex: 2 }]}>
              <Text style={{ fontSize: 22, fontWeight: 'bold', color: theme.colors.text }}>{venue.name}</Text>
              <Text style={{ color: theme.colors.primary, fontWeight: 'bold', fontSize: 16, marginBottom: 4 }}>{venue.venueType}</Text>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
                <Ionicons name="star" size={16} color="#F59E0B" />
                <Text style={{ color: theme.colors.text, fontWeight: 'bold', marginLeft: 4 }}>{venue.rating}</Text>
                <Text style={{ color: theme.colors.textSecondary, marginLeft: 4 }}>({venue.reviewCount} reviews)</Text>
              </View>
              <Text style={{ color: '#B91C1C', fontWeight: 'bold', fontSize: 18, marginTop: 4 }}>{venue.priceRange}</Text>
            </View>

            {/* About Section */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>About</Text>
              <Text style={[styles.description, { color: theme.colors.textSecondary }]}>{venue.description}</Text>
            </View>

            {/* Amenities */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Amenities</Text>
              <View style={styles.amenitiesGrid}>
                {venue.amenities.map((amenity, index) => (
                  <View key={index} style={styles.amenityItem}>
                    <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
                    <Text style={[styles.amenityText, { color: theme.colors.text }]}>{amenity}</Text>
                  </View>
                ))}
              </View>
            </View>

            {/* Services */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Services Available</Text>
              <View style={styles.servicesContainer}>
                <View style={styles.serviceItem}>
                  <Ionicons name={venue.catering ? 'checkmark-circle' : 'close-circle'} size={20} color={venue.catering ? theme.colors.success : theme.colors.error} />
                  <Text style={[styles.serviceText, { color: theme.colors.text }]}>In-house Catering</Text>
                </View>
                <View style={styles.serviceItem}>
                  <Ionicons name={venue.decoration ? 'checkmark-circle' : 'close-circle'} size={20} color={venue.decoration ? theme.colors.success : theme.colors.error} />
                  <Text style={[styles.serviceText, { color: theme.colors.text }]}>Decoration Services</Text>
                </View>
                <View style={styles.serviceItem}>
                  <Ionicons name={venue.parking ? 'checkmark-circle' : 'close-circle'} size={20} color={venue.parking ? theme.colors.success : theme.colors.error} />
                  <Text style={[styles.serviceText, { color: theme.colors.text }]}>Parking Available</Text>
                </View>
                <View style={styles.serviceItem}>
                  <Ionicons name={venue.accommodation ? 'checkmark-circle' : 'close-circle'} size={20} color={venue.accommodation ? theme.colors.success : theme.colors.error} />
                  <Text style={[styles.serviceText, { color: theme.colors.text }]}>Guest Accommodation</Text>
                </View>
              </View>
            </View>

            {/* Policies */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Venue Policies</Text>
              {venue.policies.map((policy, index) => (
                <View key={index} style={styles.policyItem}>
                  <Ionicons name="information-circle-outline" size={16} color={theme.colors.textSecondary} />
                  <Text style={[styles.policyText, { color: theme.colors.textSecondary }]}>{policy}</Text>
                </View>
              ))}
            </View>

            {/* Contact Info */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Contact Information</Text>
              <View style={styles.contactItem}>
                <Ionicons name="location-outline" size={20} color={theme.colors.textSecondary} />
                <Text style={[styles.contactText, { color: theme.colors.textSecondary }]}>{venue.address}, {venue.city}, {venue.state}</Text>
              </View>
              <TouchableOpacity style={styles.contactItem} onPress={handleCall}>
                <Ionicons name="call-outline" size={20} color={theme.colors.primary} />
                <Text style={[styles.contactText, styles.contactLink, { color: theme.colors.primary }]}>{venue.contact}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.contactItem} onPress={handleEmail}>
                <Ionicons name="mail-outline" size={20} color={theme.colors.primary} />
                <Text style={[styles.contactText, styles.contactLink, { color: theme.colors.primary }]}>{venue.email}</Text>
              </TouchableOpacity>
              {venue.website && (
                <TouchableOpacity style={styles.contactItem} onPress={handleWebsite}>
                  <Ionicons name="globe-outline" size={20} color={theme.colors.primary} />
                  <Text style={[styles.contactText, styles.contactLink, { color: theme.colors.primary }]}>Visit Website</Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Action Buttons */}
            <View style={styles.actionContainer}>
              <TouchableOpacity style={[styles.actionButton, { backgroundColor: theme.colors.primary }]} onPress={handleBooking}>
                <Ionicons name="calendar-outline" size={20} color="white" />
                <Text style={styles.actionButtonText}>Book Venue</Text>
              </TouchableOpacity>
            </View>

            {/* Quick Inquiry Form */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface, marginTop: 16 }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Quick Inquiry</Text>
              <View style={{ gap: 10 }}>
                <View>
                  <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Your Name *</Text>
                  <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8 }}>
                    <Text style={{ color: theme.colors.text }}>John Doe</Text>
                  </View>
                </View>
                <View>
                  <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Email *</Text>
                  <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8 }}>
                    <Text style={{ color: theme.colors.text }}><EMAIL></Text>
                  </View>
                </View>
                <View>
                  <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Your Message *</Text>
                  <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8, minHeight: 60 }}>
                    <Text style={{ color: theme.colors.text }}>I am interested in your venue. Please contact me.</Text>
                  </View>
                </View>
                <TouchableOpacity style={{ backgroundColor: theme.colors.primary, borderRadius: 8, paddingVertical: 14, alignItems: 'center', marginTop: 8 }} onPress={handleInquiry}>
                  <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}>Send Inquiry</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}

        {activeTab === 'Gallery' && (
          <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Photo Gallery</Text>
            <FlatList
              data={venue.gallery}
              keyExtractor={(item, idx) => idx.toString()}
              horizontal
              showsHorizontalScrollIndicator={false}
              style={{ marginTop: 12 }}
              contentContainerStyle={{ gap: 12 }}
              renderItem={({ item }) => (
                <RenderImageWithFallback uri={item} style={{ width: 220, height: 180, borderRadius: 12, backgroundColor: '#E5E7EB' }} />
              )}
            />
          </View>
        )}

        {activeTab === 'Reviews' && (
          <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Reviews</Text>
            <Text style={{ color: theme.colors.textSecondary, marginBottom: 8 }}>No reviews yet. Be the first to write a review!</Text>
            <TouchableOpacity style={{ backgroundColor: theme.colors.primary, borderRadius: 8, paddingVertical: 12, alignItems: 'center' }} onPress={handleWriteReview}>
              <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}>Write a Review</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  centered: { justifyContent: 'center', alignItems: 'center' },
  loadingText: { fontSize: 16 },
  errorText: { fontSize: 16 },
  heroContainer: { position: 'relative', height: 250 },
  heroImage: { width: '100%', height: '100%' },
  heroOverlay: { position: 'absolute', left: 0, right: 0, bottom: 0, padding: 16, backgroundColor: 'rgba(0,0,0,0.4)' },
  heroName: { color: '#fff', fontSize: 28, fontWeight: 'bold' },
  heroPrice: { color: '#fff', fontSize: 18, fontWeight: '600', marginTop: 4 },
  heroCategory: { color: '#fff', fontSize: 16, marginTop: 2 },
  heroLocationRow: { flexDirection: 'row', alignItems: 'center', marginTop: 4 },
  heroLocation: { color: '#fff', fontSize: 14, marginLeft: 4 },
  heroRatingRow: { flexDirection: 'row', alignItems: 'center', marginTop: 4 },
  heroRating: { color: '#FFD700', fontSize: 16, marginLeft: 4 },
  favoriteButton: {
    position: 'absolute', top: 16, right: 16, width: 44, height: 44, borderRadius: 22,
    justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.5)',
    shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, elevation: 3,
  },
  section: { margin: 16, padding: 16, borderRadius: 12 },
  sectionTitle: { fontSize: 18, fontWeight: 'bold', marginBottom: 12 },
  description: { fontSize: 14, lineHeight: 20 },
  amenitiesGrid: { flexDirection: 'row', flexWrap: 'wrap', gap: 12 },
  amenityItem: { flexDirection: 'row', alignItems: 'center', gap: 8, width: '48%', marginBottom: 8 },
  amenityText: { fontSize: 14, flex: 1 },
  servicesContainer: { gap: 12 },
  serviceItem: { flexDirection: 'row', alignItems: 'center', gap: 12 },
  serviceText: { fontSize: 16 },
  policyItem: { flexDirection: 'row', alignItems: 'flex-start', gap: 8, marginBottom: 8 },
  policyText: { fontSize: 14, flex: 1, lineHeight: 20 },
  contactItem: { flexDirection: 'row', alignItems: 'center', gap: 12, paddingVertical: 8 },
  contactText: { fontSize: 14, flex: 1 },
  contactLink: { textDecorationLine: 'underline' },
  actionContainer: { padding: 16, paddingBottom: 32 },
  actionButton: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center', paddingVertical: 16, borderRadius: 12, gap: 8 },
  actionButtonText: { color: 'white', fontSize: 16, fontWeight: '600' },
});
