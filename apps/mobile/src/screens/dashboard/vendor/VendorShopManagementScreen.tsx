import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../providers/ThemeProvider';
import { useAuth } from '../../../providers/AuthProvider';
import { Header } from '../../../components/Header';
import { Card, Input, Button, Badge } from '../../../components/ui';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { AppStackParamList } from '../../../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<AppStackParamList>;

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  category: string;
  images: string[];
  stock: number;
  isActive: boolean;
  rating: number;
  totalReviews: number;
  createdAt: string;
  updatedAt: string;
}

export default function VendorShopManagementScreen() {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const { user } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  const categories = ['All', 'Jewelry', 'Clothing', 'Accessories', 'Decorations', 'Gifts'];

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockProducts: Product[] = [
        {
          id: '1',
          name: 'Bridal Gold Necklace Set',
          description: 'Beautiful traditional gold necklace set perfect for brides',
          price: 85000,
          originalPrice: 95000,
          category: 'Jewelry',
          images: [
            'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400',
            'https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?w=400',
          ],
          stock: 5,
          isActive: true,
          rating: 4.8,
          totalReviews: 24,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-20T15:30:00Z',
        },
        {
          id: '2',
          name: 'Designer Lehenga',
          description: 'Elegant designer lehenga with intricate embroidery',
          price: 45000,
          category: 'Clothing',
          images: [
            'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400',
          ],
          stock: 3,
          isActive: true,
          rating: 4.6,
          totalReviews: 18,
          createdAt: '2024-01-10T14:00:00Z',
          updatedAt: '2024-01-18T09:15:00Z',
        },
        {
          id: '3',
          name: 'Wedding Decoration Package',
          description: 'Complete wedding decoration package for 200 guests',
          price: 125000,
          category: 'Decorations',
          images: [
            'https://images.unsplash.com/photo-1519741497674-611481863552?w=400',
          ],
          stock: 0,
          isActive: false,
          rating: 4.9,
          totalReviews: 32,
          createdAt: '2024-01-05T11:00:00Z',
          updatedAt: '2024-01-22T16:45:00Z',
        },
      ];
      
      setProducts(mockProducts);
    } catch (error) {
      console.error('Error loading products:', error);
      Alert.alert('Error', 'Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleProductPress = (product: Product) => {
    navigation.navigate('ProductDetail', { productId: product.id });
  };

  const handleEditProduct = (product: Product) => {
    // TODO: Navigate to edit product screen
    Alert.alert('Edit Product', `Edit ${product.name}`);
  };

  const handleToggleActive = (productId: string) => {
    setProducts(prevProducts =>
      prevProducts.map(product =>
        product.id === productId
          ? { ...product, isActive: !product.isActive }
          : product
      )
    );
  };

  const handleDeleteProduct = (productId: string) => {
    Alert.alert(
      'Delete Product',
      'Are you sure you want to delete this product?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setProducts(prevProducts =>
              prevProducts.filter(product => product.id !== productId)
            );
          },
        },
      ]
    );
  };

  const formatPrice = (price: number) => {
    return `₹${price.toLocaleString('en-IN')}`;
  };

  const renderCategoryFilter = () => (
    <View style={styles.categoryContainer}>
      <FlatList
        data={categories}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.categoryButton,
              selectedCategory === item && styles.categoryButtonActive,
              { borderColor: theme.colors.border }
            ]}
            onPress={() => setSelectedCategory(item)}
          >
            <Text
              style={[
                styles.categoryButtonText,
                { color: selectedCategory === item ? theme.colors.primary : theme.colors.textSecondary }
              ]}
            >
              {item}
            </Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );

  const renderProduct = ({ item }: { item: Product }) => (
    <Card style={styles.productCard}>
      <TouchableOpacity onPress={() => handleProductPress(item)}>
        <View style={styles.productHeader}>
          <Image source={{ uri: item.images[0] }} style={styles.productImage} />
          
          <View style={styles.productInfo}>
            <View style={styles.productTitleRow}>
              <Text style={[styles.productName, { color: theme.colors.text }]} numberOfLines={2}>
                {item.name}
              </Text>
              <Badge 
                variant={item.isActive ? 'success' : 'secondary'}
                style={styles.statusBadge}
              >
                {item.isActive ? 'Active' : 'Inactive'}
              </Badge>
            </View>
            
            <Text style={[styles.productDescription, { color: theme.colors.textSecondary }]} numberOfLines={2}>
              {item.description}
            </Text>
            
            <View style={styles.productMeta}>
              <View style={styles.priceContainer}>
                <Text style={[styles.price, { color: theme.colors.primary }]}>
                  {formatPrice(item.price)}
                </Text>
                {item.originalPrice && (
                  <Text style={[styles.originalPrice, { color: theme.colors.textSecondary }]}>
                    {formatPrice(item.originalPrice)}
                  </Text>
                )}
              </View>
              
              <View style={styles.stockContainer}>
                <Ionicons 
                  name="cube" 
                  size={14} 
                  color={item.stock > 0 ? theme.colors.success : theme.colors.destructive} 
                />
                <Text style={[
                  styles.stockText, 
                  { color: item.stock > 0 ? theme.colors.success : theme.colors.destructive }
                ]}>
                  {item.stock > 0 ? `${item.stock} in stock` : 'Out of stock'}
                </Text>
              </View>
            </View>
            
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={14} color="#FFD700" />
              <Text style={[styles.ratingText, { color: theme.colors.text }]}>
                {item.rating} ({item.totalReviews} reviews)
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
      
      <View style={styles.productActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => handleEditProduct(item)}
        >
          <Ionicons name="pencil" size={16} color="white" />
          <Text style={styles.actionButtonText}>Edit</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.actionButton, 
            { backgroundColor: item.isActive ? theme.colors.warning : theme.colors.success }
          ]}
          onPress={() => handleToggleActive(item.id)}
        >
          <Ionicons 
            name={item.isActive ? "pause" : "play"} 
            size={16} 
            color="white" 
          />
          <Text style={styles.actionButtonText}>
            {item.isActive ? 'Deactivate' : 'Activate'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.destructive }]}
          onPress={() => handleDeleteProduct(item.id)}
        >
          <Ionicons name="trash" size={16} color="white" />
          <Text style={styles.actionButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Shop Management" showBack />
      
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Input
          placeholder="Search products..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon={<Ionicons name="search" size={20} color={theme.colors.textSecondary} />}
        />
      </View>

      {/* Category Filter */}
      {renderCategoryFilter()}

      {/* Add Product Button */}
      <View style={styles.addButtonContainer}>
        <Button
          onPress={() => {
            // TODO: Navigate to add product screen
            Alert.alert('Add Product', 'Navigate to add product screen');
          }}
          style={styles.addButton}
        >
          <Ionicons name="add" size={20} color="white" />
          <Text style={styles.addButtonText}>Add New Product</Text>
        </Button>
      </View>

      {/* Products List */}
      <FlatList
        data={filteredProducts}
        renderItem={renderProduct}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.productsList}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  categoryContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
  },
  categoryButtonActive: {
    backgroundColor: 'rgba(97, 15, 19, 0.1)',
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  addButtonContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  productsList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  productCard: {
    marginBottom: 16,
    padding: 16,
  },
  productHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
  },
  productInfo: {
    flex: 1,
  },
  productTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  productDescription: {
    fontSize: 14,
    marginBottom: 8,
    lineHeight: 18,
  },
  productMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  originalPrice: {
    fontSize: 14,
    textDecorationLine: 'line-through',
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  stockText: {
    fontSize: 12,
    fontWeight: '500',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 12,
  },
  productActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    gap: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
});
