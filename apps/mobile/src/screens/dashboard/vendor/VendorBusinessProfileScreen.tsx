import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../providers/ThemeProvider';
import { useAuth } from '../../../providers/AuthProvider';
import { Header } from '../../../components/Header';
import { Card, Input, Button } from '../../../components/ui';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { AppStackParamList } from '../../../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<AppStackParamList>;

interface BusinessProfile {
  businessName: string;
  businessType: string;
  description: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  phone: string;
  email: string;
  website: string;
  socialMedia: {
    facebook: string;
    instagram: string;
    youtube: string;
  };
  businessHours: {
    monday: { open: string; close: string; isOpen: boolean };
    tuesday: { open: string; close: string; isOpen: boolean };
    wednesday: { open: string; close: string; isOpen: boolean };
    thursday: { open: string; close: string; isOpen: boolean };
    friday: { open: string; close: string; isOpen: boolean };
    saturday: { open: string; close: string; isOpen: boolean };
    sunday: { open: string; close: string; isOpen: boolean };
  };
  images: string[];
  documents: {
    businessLicense: string;
    gstCertificate: string;
    panCard: string;
  };
  isVerified: boolean;
  rating: number;
  totalReviews: number;
}

export default function VendorBusinessProfileScreen() {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const { user } = useAuth();
  const [profile, setProfile] = useState<BusinessProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);

  useEffect(() => {
    loadBusinessProfile();
  }, []);

  const loadBusinessProfile = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockProfile: BusinessProfile = {
        businessName: 'Elegant Wedding Decorations',
        businessType: 'Decorator',
        description: 'We specialize in creating beautiful and memorable wedding decorations that make your special day truly magical.',
        address: '123 Wedding Street, Decoration Lane',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600001',
        phone: '+91 9876543210',
        email: '<EMAIL>',
        website: 'www.elegantweddings.com',
        socialMedia: {
          facebook: 'facebook.com/elegantweddings',
          instagram: 'instagram.com/elegantweddings',
          youtube: 'youtube.com/elegantweddings',
        },
        businessHours: {
          monday: { open: '09:00', close: '18:00', isOpen: true },
          tuesday: { open: '09:00', close: '18:00', isOpen: true },
          wednesday: { open: '09:00', close: '18:00', isOpen: true },
          thursday: { open: '09:00', close: '18:00', isOpen: true },
          friday: { open: '09:00', close: '18:00', isOpen: true },
          saturday: { open: '10:00', close: '16:00', isOpen: true },
          sunday: { open: '10:00', close: '16:00', isOpen: false },
        },
        images: [
          'https://images.unsplash.com/photo-1519741497674-611481863552?w=400',
          'https://images.unsplash.com/photo-1606800052052-a08af7148866?w=400',
        ],
        documents: {
          businessLicense: 'BL123456789',
          gstCertificate: 'GST987654321',
          panCard: '**********',
        },
        isVerified: true,
        rating: 4.6,
        totalReviews: 89,
      };
      
      setProfile(mockProfile);
    } catch (error) {
      console.error('Error loading business profile:', error);
      Alert.alert('Error', 'Failed to load business profile');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      Alert.alert('Success', 'Business profile updated successfully');
      setEditing(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to update business profile');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    if (!profile) return;
    
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setProfile(prev => ({
        ...prev!,
        [parent]: {
          ...prev![parent as keyof BusinessProfile],
          [child]: value,
        },
      }));
    } else {
      setProfile(prev => ({
        ...prev!,
        [field]: value,
      }));
    }
  };

  const renderBusinessInfo = () => (
    <Card style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Business Information
        </Text>
        <TouchableOpacity
          onPress={() => setEditing(!editing)}
          style={styles.editButton}
        >
          <Ionicons 
            name={editing ? "checkmark" : "pencil"} 
            size={20} 
            color={theme.colors.primary} 
          />
        </TouchableOpacity>
      </View>

      <View style={styles.formContainer}>
        <Input
          label="Business Name"
          value={profile?.businessName || ''}
          onChangeText={(value) => handleInputChange('businessName', value)}
          editable={editing}
        />
        
        <Input
          label="Business Type"
          value={profile?.businessType || ''}
          onChangeText={(value) => handleInputChange('businessType', value)}
          editable={editing}
        />
        
        <Input
          label="Description"
          value={profile?.description || ''}
          onChangeText={(value) => handleInputChange('description', value)}
          multiline
          numberOfLines={4}
          editable={editing}
        />
      </View>
    </Card>
  );

  const renderContactInfo = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Contact Information
      </Text>
      
      <View style={styles.formContainer}>
        <Input
          label="Phone"
          value={profile?.phone || ''}
          onChangeText={(value) => handleInputChange('phone', value)}
          editable={editing}
          keyboardType="phone-pad"
        />
        
        <Input
          label="Email"
          value={profile?.email || ''}
          onChangeText={(value) => handleInputChange('email', value)}
          editable={editing}
          keyboardType="email-address"
        />
        
        <Input
          label="Website"
          value={profile?.website || ''}
          onChangeText={(value) => handleInputChange('website', value)}
          editable={editing}
        />
      </View>
    </Card>
  );

  const renderAddressInfo = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Address Information
      </Text>
      
      <View style={styles.formContainer}>
        <Input
          label="Address"
          value={profile?.address || ''}
          onChangeText={(value) => handleInputChange('address', value)}
          editable={editing}
          multiline
        />
        
        <View style={styles.row}>
          <Input
            label="City"
            value={profile?.city || ''}
            onChangeText={(value) => handleInputChange('city', value)}
            editable={editing}
            style={styles.halfInput}
          />
          
          <Input
            label="State"
            value={profile?.state || ''}
            onChangeText={(value) => handleInputChange('state', value)}
            editable={editing}
            style={styles.halfInput}
          />
        </View>
        
        <Input
          label="Pincode"
          value={profile?.pincode || ''}
          onChangeText={(value) => handleInputChange('pincode', value)}
          editable={editing}
          keyboardType="numeric"
        />
      </View>
    </Card>
  );

  const renderVerificationStatus = () => (
    <Card style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Verification Status
      </Text>
      
      <View style={styles.verificationContainer}>
        <View style={styles.verificationItem}>
          <Ionicons 
            name={profile?.isVerified ? "checkmark-circle" : "time"} 
            size={24} 
            color={profile?.isVerified ? theme.colors.success : theme.colors.warning} 
          />
          <Text style={[styles.verificationText, { color: theme.colors.text }]}>
            {profile?.isVerified ? 'Verified Business' : 'Verification Pending'}
          </Text>
        </View>
        
        <View style={styles.ratingContainer}>
          <Ionicons name="star" size={20} color="#FFD700" />
          <Text style={[styles.ratingText, { color: theme.colors.text }]}>
            {profile?.rating} ({profile?.totalReviews} reviews)
          </Text>
        </View>
      </View>
    </Card>
  );

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header title="Business Profile" showBack />
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Loading business profile...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Business Profile" showBack />
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderVerificationStatus()}
        {renderBusinessInfo()}
        {renderContactInfo()}
        {renderAddressInfo()}
        
        {editing && (
          <View style={styles.actionButtons}>
            <Button 
              onPress={handleSaveProfile}
              loading={loading}
              style={styles.saveButton}
            >
              Save Changes
            </Button>
            
            <Button 
              variant="outline"
              onPress={() => setEditing(false)}
              style={styles.cancelButton}
            >
              Cancel
            </Button>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  section: {
    margin: 16,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  editButton: {
    padding: 8,
  },
  formContainer: {
    gap: 16,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfInput: {
    flex: 1,
  },
  verificationContainer: {
    gap: 12,
  },
  verificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  verificationText: {
    fontSize: 16,
    fontWeight: '500',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 14,
    marginLeft: 4,
  },
  actionButtons: {
    padding: 16,
    gap: 12,
  },
  saveButton: {
    paddingVertical: 16,
  },
  cancelButton: {
    paddingVertical: 16,
  },
});
