import React, { useState, useEffect } from 'react';
import { View, Text, Alert, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Badge } from '../ui/Badge';
import { useAuth } from '../../providers/AuthProvider';
import { useTheme } from '../../providers/ThemeProvider';
import { useNavigation } from '@react-navigation/native';

interface MobileRegistrationFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
}

export function MobileRegistrationForm({ onSuccess, redirectTo }: MobileRegistrationFormProps) {
  const { register, sendOTP, verifyOTP } = useAuth();
  const { theme } = useTheme();
  const navigation = useNavigation();

  // Form state
  const [step, setStep] = useState<'details' | 'otp' | 'complete'>('details');
  
  // User details
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phoneNumber: '',
    countryCode: '+91',
    userType: 'CUSTOMER' as 'CUSTOMER' | 'VENDOR',
    agreeToTerms: false,
  });

  // OTP state
  const [otp, setOtp] = useState('');
  const [otpSent, setOtpSent] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);

  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Timer effect for OTP resend
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  const validateForm = () => {
    if (!formData.firstName.trim()) {
      setError('First name is required');
      return false;
    }

    if (!formData.lastName.trim()) {
      setError('Last name is required');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Please enter a valid email address');
      return false;
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }

    const phoneRegex = /^[6-9]\d{9}$/;
    if (!phoneRegex.test(formData.phoneNumber)) {
      setError('Please enter a valid 10-digit mobile number');
      return false;
    }

    if (!formData.agreeToTerms) {
      setError('Please agree to the terms and conditions');
      return false;
    }

    return true;
  };

  const handleSendOTP = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      setError('');
      
      const fullPhoneNumber = `${formData.countryCode}${formData.phoneNumber}`;
      await sendOTP(fullPhoneNumber);
      
      setOtpSent(true);
      setStep('otp');
      setResendTimer(30);
      
      Alert.alert('OTP Sent', `Verification code sent to ${fullPhoneNumber}`);
    } catch (error: any) {
      setError(error.message || 'Failed to send OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOTP = async () => {
    if (!otp || otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const fullPhoneNumber = `${formData.countryCode}${formData.phoneNumber}`;
      await verifyOTP(fullPhoneNumber, otp);
      
      // Proceed with registration
      await handleRegistration();
    } catch (error: any) {
      setError(error.message || 'Invalid OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRegistration = async () => {
    try {
      setLoading(true);
      setError('');
      
      const registrationData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password,
        phoneNumber: `${formData.countryCode}${formData.phoneNumber}`,
        userType: formData.userType,
      };

      await register(registrationData);
      
      setStep('complete');
      
      setTimeout(() => {
        if (onSuccess) {
          onSuccess();
        } else if (redirectTo) {
          navigation.navigate(redirectTo as never);
        } else {
          navigation.navigate('Home' as never);
        }
      }, 2000);
      
    } catch (error: any) {
      setError(error.message || 'Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (resendTimer > 0) return;
    
    try {
      setLoading(true);
      const fullPhoneNumber = `${formData.countryCode}${formData.phoneNumber}`;
      await sendOTP(fullPhoneNumber);
      setResendTimer(30);
      Alert.alert('OTP Resent', 'New verification code sent');
    } catch (error: any) {
      setError(error.message || 'Failed to resend OTP');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError(''); // Clear error when user starts typing
  };

  if (step === 'complete') {
    return (
      <Card style={{ margin: 16 }}>
        <CardContent style={{ alignItems: 'center', padding: 32 }}>
          <Ionicons name="checkmark-circle" size={80} color="#10B981" />
          <Text style={{
            fontSize: 24,
            fontWeight: '700',
            color: theme.colors.text,
            marginTop: 16,
            textAlign: 'center',
          }}>
            Welcome to Thirumanam360!
          </Text>
          <Text style={{
            fontSize: 16,
            color: theme.colors.textSecondary,
            marginTop: 8,
            textAlign: 'center',
          }}>
            Your account has been created successfully
          </Text>
          <Badge variant="success" size="lg" style={{ marginTop: 16 }}>
            Registration Complete
          </Badge>
        </CardContent>
      </Card>
    );
  }

  if (step === 'otp') {
    return (
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <Card style={{ margin: 16 }}>
          <CardHeader>
            <View style={{ alignItems: 'center' }}>
              <Ionicons name="phone-portrait" size={48} color={theme.colors.primary} />
              <CardTitle style={{ textAlign: 'center', marginTop: 8 }}>
                Verify Your Mobile Number
              </CardTitle>
              <Text style={{ 
                fontSize: 14, 
                color: theme.colors.textSecondary,
                textAlign: 'center',
                marginTop: 4
              }}>
                Enter the 6-digit code sent to {formData.countryCode}{formData.phoneNumber}
              </Text>
            </View>
          </CardHeader>

          <CardContent>
            {error ? (
              <View style={{
                backgroundColor: '#FEF2F2',
                borderColor: '#FECACA',
                borderWidth: 1,
                borderRadius: 8,
                padding: 12,
                marginBottom: 16,
                flexDirection: 'row',
                alignItems: 'center',
                gap: 8,
              }}>
                <Ionicons name="alert-circle" size={16} color="#DC2626" />
                <Text style={{ fontSize: 14, color: '#DC2626', flex: 1 }}>
                  {error}
                </Text>
              </View>
            ) : null}

            <View style={{ gap: 16 }}>
              <Input
                label="Verification Code"
                value={otp}
                onChangeText={setOtp}
                placeholder="Enter 6-digit OTP"
                keyboardType="numeric"
                maxLength={6}
                leftIcon="keypad"
              />

              <Button
                title={loading ? "Verifying..." : "Verify & Complete Registration"}
                onPress={handleVerifyOTP}
                disabled={loading}
                loading={loading}
                fullWidth
              />

              <View style={{ alignItems: 'center', marginTop: 16 }}>
                {resendTimer > 0 ? (
                  <Text style={{ fontSize: 14, color: theme.colors.textSecondary }}>
                    Resend OTP in {resendTimer}s
                  </Text>
                ) : (
                  <Button
                    title="Resend OTP"
                    onPress={handleResendOTP}
                    variant="ghost"
                    disabled={loading}
                  />
                )}
              </View>

              <Button
                title="Change Details"
                onPress={() => {
                  setStep('details');
                  setOtp('');
                  setOtpSent(false);
                }}
                variant="outline"
                fullWidth
              />
            </View>
          </CardContent>
        </Card>
      </KeyboardAvoidingView>
    );
  }

  return (
    <KeyboardAvoidingView 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
    >
      <ScrollView style={{ flex: 1 }}>
        <Card style={{ margin: 16 }}>
          <CardHeader>
            <CardTitle>Create Your Account</CardTitle>
            <Text style={{ fontSize: 14, color: theme.colors.textSecondary }}>
              Join Thirumanam360 to plan your perfect wedding
            </Text>
          </CardHeader>

          <CardContent>
            {error ? (
              <View style={{
                backgroundColor: '#FEF2F2',
                borderColor: '#FECACA',
                borderWidth: 1,
                borderRadius: 8,
                padding: 12,
                marginBottom: 16,
                flexDirection: 'row',
                alignItems: 'center',
                gap: 8,
              }}>
                <Ionicons name="alert-circle" size={16} color="#DC2626" />
                <Text style={{ fontSize: 14, color: '#DC2626', flex: 1 }}>
                  {error}
                </Text>
              </View>
            ) : null}

            <View style={{ gap: 16 }}>
              <View style={{ flexDirection: 'row', gap: 8 }}>
                <Input
                  label="First Name"
                  value={formData.firstName}
                  onChangeText={(value) => updateFormData('firstName', value)}
                  placeholder="Enter first name"
                  style={{ flex: 1 }}
                  leftIcon="person"
                />
                <Input
                  label="Last Name"
                  value={formData.lastName}
                  onChangeText={(value) => updateFormData('lastName', value)}
                  placeholder="Enter last name"
                  style={{ flex: 1 }}
                />
              </View>

              <Input
                label="Email Address"
                value={formData.email}
                onChangeText={(value) => updateFormData('email', value)}
                placeholder="Enter your email"
                keyboardType="email-address"
                autoCapitalize="none"
                leftIcon="mail"
              />

              <View style={{ flexDirection: 'row', gap: 8 }}>
                <View style={{ width: 80 }}>
                  <Input
                    label="Code"
                    value={formData.countryCode}
                    onChangeText={(value) => updateFormData('countryCode', value)}
                    placeholder="+91"
                    keyboardType="phone-pad"
                  />
                </View>
                <Input
                  label="Mobile Number"
                  value={formData.phoneNumber}
                  onChangeText={(value) => updateFormData('phoneNumber', value)}
                  placeholder="Enter mobile number"
                  keyboardType="phone-pad"
                  maxLength={10}
                  leftIcon="call"
                  style={{ flex: 1 }}
                />
              </View>

              <Input
                label="Password"
                value={formData.password}
                onChangeText={(value) => updateFormData('password', value)}
                placeholder="Create a password"
                secureTextEntry={!showPassword}
                leftIcon="lock-closed"
                rightIcon={showPassword ? "eye-off" : "eye"}
                onRightIconPress={() => setShowPassword(!showPassword)}
              />

              <Input
                label="Confirm Password"
                value={formData.confirmPassword}
                onChangeText={(value) => updateFormData('confirmPassword', value)}
                placeholder="Confirm your password"
                secureTextEntry={!showConfirmPassword}
                leftIcon="lock-closed"
                rightIcon={showConfirmPassword ? "eye-off" : "eye"}
                onRightIconPress={() => setShowConfirmPassword(!showConfirmPassword)}
              />

              <View style={{ flexDirection: 'row', gap: 8 }}>
                <Button
                  title="Customer"
                  onPress={() => updateFormData('userType', 'CUSTOMER')}
                  variant={formData.userType === 'CUSTOMER' ? 'default' : 'outline'}
                  style={{ flex: 1 }}
                  icon="person"
                />
                <Button
                  title="Vendor"
                  onPress={() => updateFormData('userType', 'VENDOR')}
                  variant={formData.userType === 'VENDOR' ? 'default' : 'outline'}
                  style={{ flex: 1 }}
                  icon="briefcase"
                />
              </View>

              <Button
                title={loading ? "Sending OTP..." : "Send Verification Code"}
                onPress={handleSendOTP}
                disabled={loading}
                loading={loading}
                fullWidth
              />

              <View style={{ 
                flexDirection: 'row', 
                justifyContent: 'center', 
                alignItems: 'center',
                marginTop: 16,
                gap: 4
              }}>
                <Text style={{ fontSize: 14, color: theme.colors.textSecondary }}>
                  Already have an account?
                </Text>
                <Button
                  title="Sign In"
                  onPress={() => navigation.navigate('Login' as never)}
                  variant="ghost"
                  size="sm"
                />
              </View>
            </View>
          </CardContent>
        </Card>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
