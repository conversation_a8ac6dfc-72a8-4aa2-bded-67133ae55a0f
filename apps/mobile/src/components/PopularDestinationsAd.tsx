import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, ImageBackground, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Badge } from './ui/Badge';
import { Button } from './ui/Button';
import { useTheme } from '../providers/ThemeProvider';
import { useNavigation } from '@react-navigation/native';

const { width } = Dimensions.get('window');

interface PopularDestinationsAdProps {
  showPromotionalBadges?: boolean;
  showSpecialOffers?: boolean;
  onDestinationClick?: (cityName: string, cityData: any) => void;
  enableRedirects?: boolean;
  redirectToVendors?: boolean;
}

interface CityData {
  name: string;
  state: string;
  image: string;
  vendorCount: number;
  rating: number;
  specialOffer?: string;
  isPopular?: boolean;
  isTrending?: boolean;
  gradient: string[];
}

const popularCities: CityData[] = [
  {
    name: 'Chennai',
    state: 'Tamil Nadu',
    image: 'https://images.unsplash.com/photo-1582510003544-4d00b7f74220?w=400',
    vendorCount: 1250,
    rating: 4.8,
    specialOffer: '20% Off Wedding Packages',
    isPopular: true,
    isTrending: true,
    gradient: ['#610f13', '#dc2626'],
  },
  {
    name: 'Coimbatore',
    state: 'Tamil Nadu',
    image: 'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=400',
    vendorCount: 850,
    rating: 4.7,
    specialOffer: 'Free Decoration Consultation',
    isPopular: true,
    gradient: ['#22c55e', '#16a34a'],
  },
  {
    name: 'Madurai',
    state: 'Tamil Nadu',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
    vendorCount: 650,
    rating: 4.6,
    isTrending: true,
    gradient: ['#f6c244', '#fb923c'],
  },
  {
    name: 'Trichy',
    state: 'Tamil Nadu',
    image: 'https://images.unsplash.com/photo-1566552881560-0be862a7c445?w=400',
    vendorCount: 420,
    rating: 4.5,
    specialOffer: 'Complimentary Photography Session',
    gradient: ['#ec4899', '#db2777'],
  },
  {
    name: 'Salem',
    state: 'Tamil Nadu',
    image: 'https://images.unsplash.com/photo-1519167758481-83f29c1fe8ea?w=400',
    vendorCount: 380,
    rating: 4.4,
    gradient: ['#6366f1', '#4f46e5'],
  },
  {
    name: 'Tirunelveli',
    state: 'Tamil Nadu',
    image: 'https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?w=400',
    vendorCount: 290,
    rating: 4.3,
    gradient: ['#a855f7', '#9333ea'],
  },
];

export function PopularDestinationsAd({
  showPromotionalBadges = true,
  showSpecialOffers = true,
  onDestinationClick,
  enableRedirects = true,
  redirectToVendors = true,
}: PopularDestinationsAdProps) {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const [pressedCity, setPressedCity] = useState<number | null>(null);

  const handleCityPress = (city: CityData, index: number) => {
    if (onDestinationClick) {
      onDestinationClick(city.name, city);
    } else if (enableRedirects) {
      if (redirectToVendors) {
        navigation.navigate('Vendors' as never, { 
          city: city.name,
          state: city.state 
        } as never);
      } else {
        navigation.navigate('Venues' as never, { 
          city: city.name,
          state: city.state 
        } as never);
      }
    }
  };

  const handleExploreAll = () => {
    navigation.navigate('Vendors' as never, { showAllCities: true } as never);
  };

  const renderCityCard = (city: CityData, index: number) => {
    const cardWidth = (width - 48) / 2; // 2 columns with margins

    return (
      <TouchableOpacity
        key={index}
        onPress={() => handleCityPress(city, index)}
        onPressIn={() => setPressedCity(index)}
        onPressOut={() => setPressedCity(null)}
        style={{
          width: cardWidth,
          marginBottom: 16,
          opacity: pressedCity === index ? 0.8 : 1,
        }}
      >
        <Card style={{ height: 200, overflow: 'hidden' }}>
          <ImageBackground
            source={{ uri: city.image }}
            style={{ flex: 1 }}
            resizeMode="cover"
          >
            <LinearGradient
              colors={[...city.gradient.map(color => `${color}CC`), `${city.gradient[1]}E6`]}
              style={{ flex: 1, padding: 12, justifyContent: 'space-between' }}
            >
              {/* Top badges */}
              <View style={{ flexDirection: 'row', gap: 6, flexWrap: 'wrap' }}>
                {showPromotionalBadges && city.isPopular && (
                  <Badge variant="warning" size="sm">
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}>
                      <Ionicons name="star" size={10} color="#fff" />
                      <Text style={{ color: '#fff', fontSize: 9 }}>Popular</Text>
                    </View>
                  </Badge>
                )}
                
                {showPromotionalBadges && city.isTrending && (
                  <Badge variant="success" size="sm">
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}>
                      <Ionicons name="trending-up" size={10} color="#fff" />
                      <Text style={{ color: '#fff', fontSize: 9 }}>Trending</Text>
                    </View>
                  </Badge>
                )}
              </View>

              {/* Bottom content */}
              <View>
                <Text style={{
                  fontSize: 16,
                  fontWeight: '700',
                  color: '#fff',
                  marginBottom: 4,
                  textShadowColor: 'rgba(0,0,0,0.5)',
                  textShadowOffset: { width: 1, height: 1 },
                  textShadowRadius: 2,
                }}>
                  {city.name}
                </Text>
                
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginBottom: 6 }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}>
                    <Ionicons name="business" size={12} color="#fff" />
                    <Text style={{ fontSize: 11, color: '#fff', fontWeight: '500' }}>
                      {city.vendorCount}+ vendors
                    </Text>
                  </View>
                  
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}>
                    <Ionicons name="star" size={12} color="#F59E0B" />
                    <Text style={{ fontSize: 11, color: '#fff', fontWeight: '500' }}>
                      {city.rating}
                    </Text>
                  </View>
                </View>

                {showSpecialOffers && city.specialOffer && (
                  <View style={{
                    backgroundColor: 'rgba(255,255,255,0.9)',
                    paddingHorizontal: 6,
                    paddingVertical: 2,
                    borderRadius: 4,
                    alignSelf: 'flex-start',
                  }}>
                    <Text style={{
                      fontSize: 9,
                      fontWeight: '600',
                      color: city.gradient[0],
                    }}>
                      {city.specialOffer}
                    </Text>
                  </View>
                )}
              </View>
            </LinearGradient>
          </ImageBackground>
        </Card>
      </TouchableOpacity>
    );
  };

  return (
    <Card style={{ margin: 16 }}>
      <CardHeader>
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <Ionicons name="location" size={24} color={theme.colors.primary} />
            <CardTitle>Popular Wedding Destinations</CardTitle>
          </View>
          
          {showPromotionalBadges && (
            <Badge variant="default" size="sm">
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}>
                <Ionicons name="sparkles" size={10} color="#fff" />
                <Text style={{ color: '#fff', fontSize: 9 }}>Featured</Text>
              </View>
            </Badge>
          )}
        </View>
        
        <Text style={{
          fontSize: 14,
          color: theme.colors.textSecondary,
          marginTop: 4,
        }}>
          Discover the most sought-after wedding destinations with top-rated vendors
        </Text>
      </CardHeader>

      <CardContent>
        <View style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          justifyContent: 'space-between',
          marginBottom: 16,
        }}>
          {popularCities.map((city, index) => renderCityCard(city, index))}
        </View>

        <Button
          title="Explore All Cities"
          onPress={handleExploreAll}
          variant="outline"
          icon="arrow-forward"
          iconPosition="right"
          fullWidth
        />
      </CardContent>
    </Card>
  );
}
