import React, { useState, useEffect } from 'react';
import { View, Text, Alert, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Badge } from '../ui/Badge';
import { useAuth } from '../../providers/AuthProvider';
import { useTheme } from '../../providers/ThemeProvider';

interface NewsletterSubscriptionProps {
  source: string;
  variant?: 'default' | 'compact' | 'detailed';
  showInterests?: boolean;
  defaultEmail?: string;
  onSuccess?: () => void;
}

const INTEREST_OPTIONS = [
  { value: 'PHOTOGRAPHY', label: 'Photography', icon: 'camera' },
  { value: 'VIDEOGRAPHY', label: 'Videography', icon: 'videocam' },
  { value: 'CATERING', label: 'Catering', icon: 'restaurant' },
  { value: 'DECORATION', label: 'Decoration', icon: 'flower' },
  { value: 'MAKEUP', label: 'Makeup & Beauty', icon: 'brush' },
  { value: 'VENUES', label: 'Venues', icon: 'business' },
  { value: 'SHOPPING', label: 'Wedding Shopping', icon: 'bag' },
  { value: 'PLANNING', label: 'Wedding Planning', icon: 'calendar' },
  { value: 'HONEYMOON', label: 'Honeymoon', icon: 'airplane' },
  { value: 'JEWELRY', label: 'Jewelry', icon: 'diamond' },
  { value: 'INVITATIONS', label: 'Invitations', icon: 'mail' },
  { value: 'MUSIC', label: 'Music & Entertainment', icon: 'musical-notes' }
];

export function NewsletterSubscription({
  source,
  variant = 'default',
  showInterests = false,
  defaultEmail = '',
  onSuccess
}: NewsletterSubscriptionProps) {
  const { user, userProfile } = useAuth();
  const { theme } = useTheme();
  
  const [formData, setFormData] = useState({
    email: defaultEmail || userProfile?.email || '',
    firstName: userProfile?.firstName || '',
    lastName: userProfile?.lastName || '',
    interests: [] as string[],
    agreeToTerms: false
  });
  
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (userProfile) {
      setFormData(prev => ({
        ...prev,
        email: prev.email || userProfile.email || '',
        firstName: prev.firstName || userProfile.firstName || '',
        lastName: prev.lastName || userProfile.lastName || ''
      }));
    }
  }, [userProfile]);

  const validateForm = () => {
    if (!formData.email) {
      setError('Email address is required');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Please enter a valid email address');
      return false;
    }

    if (!formData.agreeToTerms) {
      setError('Please agree to receive newsletter emails');
      return false;
    }

    return true;
  };

  const handleSubscribe = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      setError('');
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock subscription logic
      const subscriptionData = {
        email: formData.email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        interests: formData.interests,
        source: source,
        subscribedAt: new Date().toISOString()
      };
      
      console.log('Newsletter subscription:', subscriptionData);
      
      setSuccess(true);
      
      if (onSuccess) {
        onSuccess();
      }
      
      // Reset form after success
      setTimeout(() => {
        setSuccess(false);
        setFormData(prev => ({
          ...prev,
          interests: [],
          agreeToTerms: false
        }));
      }, 3000);
      
    } catch (error: any) {
      setError(error.message || 'Failed to subscribe. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const toggleInterest = (interest: string) => {
    setFormData(prev => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest]
    }));
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError(''); // Clear error when user starts typing
  };

  if (success) {
    return (
      <Card>
        <CardContent style={{ alignItems: 'center', padding: 32 }}>
          <Ionicons name="checkmark-circle" size={64} color="#10B981" />
          <Text style={{
            fontSize: 20,
            fontWeight: '700',
            color: theme.colors.text,
            marginTop: 16,
            textAlign: 'center',
          }}>
            Successfully Subscribed!
          </Text>
          <Text style={{
            fontSize: 14,
            color: theme.colors.textSecondary,
            textAlign: 'center',
            marginTop: 8,
          }}>
            Thank you for subscribing to our newsletter. You'll receive wedding tips, vendor updates, and exclusive offers.
          </Text>
          <Badge variant="success" style={{ marginTop: 16 }}>
            Welcome to Thirumanam360!
          </Badge>
        </CardContent>
      </Card>
    );
  }

  if (variant === 'compact') {
    return (
      <Card>
        <CardContent style={{ padding: 16 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginBottom: 12 }}>
            <Ionicons name="mail" size={20} color={theme.colors.primary} />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: theme.colors.text,
            }}>
              Stay Updated
            </Text>
          </View>
          
          {error ? (
            <View style={{
              backgroundColor: '#FEF2F2',
              borderColor: '#FECACA',
              borderWidth: 1,
              borderRadius: 6,
              padding: 8,
              marginBottom: 12,
              flexDirection: 'row',
              alignItems: 'center',
              gap: 6,
            }}>
              <Ionicons name="alert-circle" size={14} color="#DC2626" />
              <Text style={{ fontSize: 12, color: '#DC2626', flex: 1 }}>
                {error}
              </Text>
            </View>
          ) : null}
          
          <View style={{ gap: 12 }}>
            <Input
              value={formData.email}
              onChangeText={(value) => updateFormData('email', value)}
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              leftIcon="mail"
            />
            
            <TouchableOpacity
              onPress={() => updateFormData('agreeToTerms', !formData.agreeToTerms)}
              style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}
            >
              <View style={{
                width: 16,
                height: 16,
                borderRadius: 3,
                borderWidth: 2,
                borderColor: formData.agreeToTerms ? theme.colors.primary : theme.colors.border,
                backgroundColor: formData.agreeToTerms ? theme.colors.primary : 'transparent',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                {formData.agreeToTerms && (
                  <Ionicons name="checkmark" size={10} color="#fff" />
                )}
              </View>
              <Text style={{ fontSize: 12, color: theme.colors.textSecondary, flex: 1 }}>
                I agree to receive newsletter emails
              </Text>
            </TouchableOpacity>
            
            <Button
              title={loading ? "Subscribing..." : "Subscribe"}
              onPress={handleSubscribe}
              disabled={loading}
              loading={loading}
              size="sm"
              fullWidth
            />
          </View>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
          <Ionicons name="mail" size={24} color={theme.colors.primary} />
          <CardTitle>Subscribe to Our Newsletter</CardTitle>
        </View>
        <Text style={{ fontSize: 14, color: theme.colors.textSecondary }}>
          Get the latest wedding tips, vendor updates, and exclusive offers delivered to your inbox.
        </Text>
      </CardHeader>

      <CardContent>
        {error ? (
          <View style={{
            backgroundColor: '#FEF2F2',
            borderColor: '#FECACA',
            borderWidth: 1,
            borderRadius: 8,
            padding: 12,
            marginBottom: 16,
            flexDirection: 'row',
            alignItems: 'center',
            gap: 8,
          }}>
            <Ionicons name="alert-circle" size={16} color="#DC2626" />
            <Text style={{ fontSize: 14, color: '#DC2626', flex: 1 }}>
              {error}
            </Text>
          </View>
        ) : null}

        <View style={{ gap: 16 }}>
          <View style={{ flexDirection: 'row', gap: 12 }}>
            <Input
              label="First Name"
              value={formData.firstName}
              onChangeText={(value) => updateFormData('firstName', value)}
              placeholder="First name"
              style={{ flex: 1 }}
            />
            <Input
              label="Last Name"
              value={formData.lastName}
              onChangeText={(value) => updateFormData('lastName', value)}
              placeholder="Last name"
              style={{ flex: 1 }}
            />
          </View>

          <Input
            label="Email Address *"
            value={formData.email}
            onChangeText={(value) => updateFormData('email', value)}
            placeholder="Enter your email address"
            keyboardType="email-address"
            autoCapitalize="none"
            leftIcon="mail"
          />

          {showInterests && (
            <View>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: theme.colors.text,
                marginBottom: 12,
              }}>
                Interests (Optional)
              </Text>
              <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8 }}>
                {INTEREST_OPTIONS.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    onPress={() => toggleInterest(option.value)}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 6,
                      paddingHorizontal: 12,
                      paddingVertical: 8,
                      borderRadius: 20,
                      borderWidth: 1,
                      borderColor: formData.interests.includes(option.value) 
                        ? theme.colors.primary 
                        : theme.colors.border,
                      backgroundColor: formData.interests.includes(option.value) 
                        ? `${theme.colors.primary}10` 
                        : 'transparent',
                    }}
                  >
                    <Ionicons 
                      name={option.icon as any} 
                      size={14} 
                      color={formData.interests.includes(option.value) 
                        ? theme.colors.primary 
                        : theme.colors.textSecondary
                      } 
                    />
                    <Text style={{
                      fontSize: 12,
                      color: formData.interests.includes(option.value) 
                        ? theme.colors.primary 
                        : theme.colors.text,
                    }}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}

          <TouchableOpacity
            onPress={() => updateFormData('agreeToTerms', !formData.agreeToTerms)}
            style={{ flexDirection: 'row', alignItems: 'flex-start', gap: 12 }}
          >
            <View style={{
              width: 20,
              height: 20,
              borderRadius: 4,
              borderWidth: 2,
              borderColor: formData.agreeToTerms ? theme.colors.primary : theme.colors.border,
              backgroundColor: formData.agreeToTerms ? theme.colors.primary : 'transparent',
              alignItems: 'center',
              justifyContent: 'center',
              marginTop: 2,
            }}>
              {formData.agreeToTerms && (
                <Ionicons name="checkmark" size={12} color="#fff" />
              )}
            </View>
            <Text style={{ fontSize: 14, color: theme.colors.textSecondary, flex: 1 }}>
              I agree to receive newsletter emails with wedding tips, vendor updates, and promotional offers. 
              I can unsubscribe at any time.
            </Text>
          </TouchableOpacity>

          <Button
            title={loading ? "Subscribing..." : "Subscribe to Newsletter"}
            onPress={handleSubscribe}
            disabled={loading}
            loading={loading}
            icon="mail"
            fullWidth
          />

          <Text style={{
            fontSize: 12,
            color: theme.colors.textSecondary,
            textAlign: 'center',
          }}>
            We respect your privacy. Your email will never be shared with third parties.
          </Text>
        </View>
      </CardContent>
    </Card>
  );
}
