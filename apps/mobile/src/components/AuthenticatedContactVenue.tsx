import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Linking, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Button } from './ui/Button';
import { Badge } from './ui/Badge';
import { VenueInquiryForm } from './VenueInquiryForm';
import { useAuth } from '../providers/AuthProvider';
import { useTheme } from '../providers/ThemeProvider';
import { useNavigation } from '@react-navigation/native';

interface AuthenticatedContactVenueProps {
  venue: {
    id: string;
    userId: string;
    name: string;
    type?: string;
    capacity?: number;
    contactPhone?: string;
    contactEmail?: string;
    website?: string;
    rating?: number;
    reviewCount?: number;
    priceRange?: string;
    city?: string;
    state?: string;
  };
  showInquiryForm?: boolean;
}

export function AuthenticatedContactVenue({ 
  venue, 
  showInquiryForm = true 
}: AuthenticatedContactVenueProps) {
  const { isAuthenticated, isLoading, user } = useAuth();
  const { theme } = useTheme();
  const navigation = useNavigation();
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [showVenueInquiry, setShowVenueInquiry] = useState(false);

  const handleContactAction = (action: string) => {
    if (!isAuthenticated) {
      setShowLoginPrompt(true);
      return;
    }

    switch (action) {
      case 'call':
        if (venue.contactPhone) {
          Linking.openURL(`tel:${venue.contactPhone}`);
        } else {
          Alert.alert('Contact Not Available', 'Phone number not provided by venue');
        }
        break;
      case 'email':
        if (venue.contactEmail) {
          Linking.openURL(`mailto:${venue.contactEmail}`);
        } else {
          Alert.alert('Email Not Available', 'Email address not provided by venue');
        }
        break;
      case 'website':
        if (venue.website) {
          Linking.openURL(venue.website);
        } else {
          Alert.alert('Website Not Available', 'Website not provided by venue');
        }
        break;
      case 'inquiry':
        setShowVenueInquiry(true);
        break;
      case 'whatsapp':
        if (venue.contactPhone) {
          const message = `Hi ${venue.name}, I'm interested in booking your venue for a wedding. Can you please provide more details about availability and pricing?`;
          Linking.openURL(`whatsapp://send?phone=${venue.contactPhone}&text=${encodeURIComponent(message)}`);
        } else {
          Alert.alert('WhatsApp Not Available', 'Phone number not provided by venue');
        }
        break;
    }
  };

  const handleLogin = () => {
    setShowLoginPrompt(false);
    navigation.navigate('Login' as never);
  };

  const handleSignup = () => {
    setShowLoginPrompt(false);
    navigation.navigate('Signup' as never);
  };

  if (isLoading) {
    return (
      <Card style={{ margin: 16 }}>
        <CardContent style={{ padding: 20, alignItems: 'center' }}>
          <Text style={{ color: theme.colors.textSecondary }}>Loading...</Text>
        </CardContent>
      </Card>
    );
  }

  if (showVenueInquiry) {
    return (
      <VenueInquiryForm
        venueUserId={venue.userId}
        venueId={venue.id}
        venueName={venue.name}
        venueType={venue.type}
        onClose={() => setShowVenueInquiry(false)}
      />
    );
  }

  if (showLoginPrompt) {
    return (
      <Card style={{ margin: 16 }}>
        <CardHeader>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <Ionicons name="lock-closed" size={24} color={theme.colors.primary} />
            <CardTitle>Authentication Required</CardTitle>
          </View>
        </CardHeader>
        
        <CardContent>
          <View style={{ alignItems: 'center', gap: 16 }}>
            <Ionicons name="business" size={64} color={theme.colors.primary} />
            
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: theme.colors.text,
              textAlign: 'center',
            }}>
              Sign in to Contact Venue
            </Text>
            
            <Text style={{
              fontSize: 14,
              color: theme.colors.textSecondary,
              textAlign: 'center',
              lineHeight: 20,
            }}>
              To protect our venues from spam and ensure quality inquiries, 
              please sign in to access contact information and send booking requests.
            </Text>

            <View style={{ 
              backgroundColor: theme.colors.muted,
              padding: 12,
              borderRadius: 8,
              width: '100%',
            }}>
              <Text style={{
                fontSize: 12,
                color: theme.colors.textSecondary,
                textAlign: 'center',
                fontStyle: 'italic',
              }}>
                Your information is secure and will only be used to facilitate 
                communication with wedding venues.
              </Text>
            </View>

            <View style={{ flexDirection: 'row', gap: 12, width: '100%' }}>
              <Button
                title="Sign In"
                onPress={handleLogin}
                style={{ flex: 1 }}
                icon="log-in"
              />
              <Button
                title="Sign Up"
                onPress={handleSignup}
                variant="outline"
                style={{ flex: 1 }}
                icon="person-add"
              />
            </View>

            <Button
              title="Cancel"
              onPress={() => setShowLoginPrompt(false)}
              variant="ghost"
              fullWidth
            />
          </View>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card style={{ margin: 16 }}>
      <CardHeader>
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <Ionicons name="business" size={24} color={theme.colors.primary} />
            <CardTitle>Contact Venue</CardTitle>
          </View>
          
          {venue.rating && (
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
              <Ionicons name="star" size={16} color="#F59E0B" />
              <Text style={{ fontSize: 14, fontWeight: '600', color: theme.colors.text }}>
                {venue.rating.toFixed(1)}
              </Text>
              {venue.reviewCount && (
                <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                  ({venue.reviewCount})
                </Text>
              )}
            </View>
          )}
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginTop: 8, flexWrap: 'wrap' }}>
          <Badge variant="default" size="sm">
            {venue.name}
          </Badge>
          {venue.type && (
            <Badge variant="outline" size="sm">
              {venue.type}
            </Badge>
          )}
          {venue.capacity && (
            <Badge variant="secondary" size="sm">
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}>
                <Ionicons name="people" size={10} color="#fff" />
                <Text style={{ color: '#fff', fontSize: 10 }}>{venue.capacity} guests</Text>
              </View>
            </Badge>
          )}
          {venue.priceRange && (
            <Badge variant="success" size="sm">
              {venue.priceRange}
            </Badge>
          )}
        </View>

        {(venue.city || venue.state) && (
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4, marginTop: 4 }}>
            <Ionicons name="location" size={14} color={theme.colors.textSecondary} />
            <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
              {venue.city}{venue.city && venue.state ? ', ' : ''}{venue.state}
            </Text>
          </View>
        )}
      </CardHeader>

      <CardContent>
        <View style={{ gap: 12 }}>
          {/* Quick Actions */}
          <View style={{ flexDirection: 'row', gap: 8, flexWrap: 'wrap' }}>
            <Button
              title="Call"
              onPress={() => handleContactAction('call')}
              variant="default"
              size="sm"
              icon="call"
              style={{ flex: 1, minWidth: 80 }}
            />
            
            <Button
              title="Email"
              onPress={() => handleContactAction('email')}
              variant="outline"
              size="sm"
              icon="mail"
              style={{ flex: 1, minWidth: 80 }}
            />
            
            <Button
              title="WhatsApp"
              onPress={() => handleContactAction('whatsapp')}
              variant="secondary"
              size="sm"
              icon="logo-whatsapp"
              style={{ flex: 1, minWidth: 80 }}
            />
          </View>

          {/* Website Link */}
          {venue.website && (
            <Button
              title="Visit Website"
              onPress={() => handleContactAction('website')}
              variant="outline"
              icon="globe"
              iconPosition="left"
              fullWidth
            />
          )}

          {/* Venue Inquiry */}
          {showInquiryForm && (
            <Button
              title="Send Venue Inquiry"
              onPress={() => handleContactAction('inquiry')}
              variant="default"
              icon="calendar"
              iconPosition="left"
              fullWidth
            />
          )}

          {/* Contact Information Display */}
          <View style={{ 
            backgroundColor: theme.colors.muted,
            padding: 12,
            borderRadius: 8,
            gap: 8,
          }}>
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: theme.colors.text,
              marginBottom: 4,
            }}>
              Venue Information
            </Text>
            
            {venue.contactPhone && (
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Ionicons name="call" size={16} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 14, color: theme.colors.text }}>
                  {venue.contactPhone}
                </Text>
              </View>
            )}
            
            {venue.contactEmail && (
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Ionicons name="mail" size={16} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 14, color: theme.colors.text }}>
                  {venue.contactEmail}
                </Text>
              </View>
            )}
            
            {venue.website && (
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Ionicons name="globe" size={16} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 14, color: theme.colors.text }}>
                  {venue.website}
                </Text>
              </View>
            )}

            {venue.capacity && (
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Ionicons name="people" size={16} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 14, color: theme.colors.text }}>
                  Capacity: {venue.capacity} guests
                </Text>
              </View>
            )}
          </View>

          {/* Security Notice */}
          <View style={{
            backgroundColor: '#F0F9FF',
            borderColor: '#0EA5E9',
            borderWidth: 1,
            borderRadius: 8,
            padding: 12,
            flexDirection: 'row',
            alignItems: 'center',
            gap: 8,
          }}>
            <Ionicons name="shield-checkmark" size={16} color="#0EA5E9" />
            <Text style={{ fontSize: 12, color: '#0369A1', flex: 1 }}>
              Your contact information is protected and will only be shared with this venue.
            </Text>
          </View>
        </View>
      </CardContent>
    </Card>
  );
}
