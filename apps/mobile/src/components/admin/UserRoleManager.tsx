import React, { useState } from 'react';
import { View, Text, ScrollView, Alert, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Badge } from '../ui/Badge';
import { useAuth } from '../../providers/AuthProvider';
import { useTheme } from '../../providers/ThemeProvider';

type UserRole = 'customer' | 'vendor' | 'admin' | 'super_admin';

interface RoleUpdateResult {
  success: boolean;
  message: string;
  newRole?: UserRole;
}

interface UserRoleManagerProps {
  targetUserId?: string;
  targetUserEmail?: string;
  currentRole?: UserRole;
  onRoleUpdated?: (result: RoleUpdateResult) => void;
}

export function UserRoleManager({ 
  targetUserId, 
  targetUserEmail, 
  currentRole = 'customer',
  onRoleUpdated 
}: UserRoleManagerProps) {
  const { user, userProfile } = useAuth();
  const { theme } = useTheme();
  
  const [selectedRole, setSelectedRole] = useState<UserRole>(currentRole);
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchUserId, setSearchUserId] = useState(targetUserId || '');
  const [searchEmail, setSearchEmail] = useState(targetUserEmail || '');

  const roleOptions = [
    { 
      value: 'customer' as UserRole, 
      label: 'Customer', 
      icon: 'person', 
      color: '#3B82F6',
      description: 'Regular customer with basic access'
    },
    { 
      value: 'vendor' as UserRole, 
      label: 'Vendor', 
      icon: 'briefcase', 
      color: '#10B981',
      description: 'Service provider with vendor dashboard access'
    },
    { 
      value: 'admin' as UserRole, 
      label: 'Administrator', 
      icon: 'shield', 
      color: '#F59E0B',
      description: 'Admin with management capabilities'
    },
    { 
      value: 'super_admin' as UserRole, 
      label: 'Super Administrator', 
      icon: 'diamond', 
      color: '#8B5CF6',
      description: 'Full system access and control'
    },
  ];

  const getCurrentRoleBadge = (role: UserRole) => {
    const roleConfig = roleOptions.find(r => r.value === role);
    if (!roleConfig) return null;

    return (
      <Badge 
        variant="default" 
        style={{ backgroundColor: roleConfig.color }}
      >
        {roleConfig.label}
      </Badge>
    );
  };

  const canUpdateRole = () => {
    if (!userProfile) return false;
    
    // Only admins and super admins can update roles
    return userProfile.role === 'admin' || userProfile.role === 'super_admin';
  };

  const canAssignRole = (role: UserRole) => {
    if (!userProfile) return false;
    
    // Super admins can assign any role
    if (userProfile.role === 'super_admin') return true;
    
    // Admins can assign customer and vendor roles only
    if (userProfile.role === 'admin') {
      return role === 'customer' || role === 'vendor';
    }
    
    return false;
  };

  const handleRoleUpdate = async () => {
    if (!canUpdateRole()) {
      Alert.alert('Error', 'You do not have permission to update user roles');
      return;
    }

    if (!searchUserId && !searchEmail) {
      Alert.alert('Error', 'Please provide either User ID or Email');
      return;
    }

    if (!reason.trim()) {
      Alert.alert('Error', 'Please provide a reason for the role change');
      return;
    }

    if (!canAssignRole(selectedRole)) {
      Alert.alert('Error', `You do not have permission to assign ${selectedRole} role`);
      return;
    }

    Alert.alert(
      'Confirm Role Update',
      `Are you sure you want to change the user's role to ${selectedRole}?\n\nReason: ${reason}`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Update', 
          style: 'destructive',
          onPress: performRoleUpdate
        }
      ]
    );
  };

  const performRoleUpdate = async () => {
    try {
      setLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const result: RoleUpdateResult = {
        success: true,
        message: `User role successfully updated to ${selectedRole}`,
        newRole: selectedRole
      };
      
      Alert.alert('Success', result.message);
      
      if (onRoleUpdated) {
        onRoleUpdated(result);
      }
      
      // Reset form
      setReason('');
      
    } catch (error: any) {
      const result: RoleUpdateResult = {
        success: false,
        message: error.message || 'Failed to update user role'
      };
      
      Alert.alert('Error', result.message);
      
      if (onRoleUpdated) {
        onRoleUpdated(result);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSearchUser = async () => {
    if (!searchUserId && !searchEmail) {
      Alert.alert('Error', 'Please provide either User ID or Email');
      return;
    }

    try {
      setLoading(true);
      
      // Simulate user search
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock user data
      const mockUser = {
        id: searchUserId || 'user123',
        email: searchEmail || '<EMAIL>',
        name: 'John Doe',
        currentRole: 'customer' as UserRole,
        createdAt: '2024-01-15',
        lastLogin: '2024-01-20'
      };
      
      setSelectedRole(mockUser.currentRole);
      Alert.alert('User Found', `Found user: ${mockUser.name} (${mockUser.email})\nCurrent Role: ${mockUser.currentRole}`);
      
    } catch (error: any) {
      Alert.alert('Error', 'User not found or search failed');
    } finally {
      setLoading(false);
    }
  };

  if (!canUpdateRole()) {
    return (
      <Card style={{ margin: 16 }}>
        <CardContent style={{ alignItems: 'center', padding: 32 }}>
          <Ionicons name="lock-closed" size={48} color={theme.colors.textSecondary} />
          <Text style={{
            fontSize: 18,
            fontWeight: '600',
            color: theme.colors.text,
            marginTop: 16,
            textAlign: 'center',
          }}>
            Access Denied
          </Text>
          <Text style={{
            fontSize: 14,
            color: theme.colors.textSecondary,
            textAlign: 'center',
            marginTop: 8,
          }}>
            You do not have permission to manage user roles
          </Text>
        </CardContent>
      </Card>
    );
  }

  return (
    <ScrollView style={{ flex: 1 }}>
      <Card style={{ margin: 16 }}>
        <CardHeader>
          <CardTitle>User Role Management</CardTitle>
          <Text style={{ fontSize: 14, color: theme.colors.textSecondary }}>
            Update user roles and permissions
          </Text>
        </CardHeader>

        <CardContent>
          <View style={{ gap: 16 }}>
            {/* User Search Section */}
            <View>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: theme.colors.text,
                marginBottom: 12,
              }}>
                Find User
              </Text>
              
              <View style={{ gap: 12 }}>
                <Input
                  label="User ID"
                  value={searchUserId}
                  onChangeText={setSearchUserId}
                  placeholder="Enter user ID"
                  leftIcon="person"
                />
                
                <Text style={{
                  fontSize: 14,
                  color: theme.colors.textSecondary,
                  textAlign: 'center',
                }}>
                  OR
                </Text>
                
                <Input
                  label="Email Address"
                  value={searchEmail}
                  onChangeText={setSearchEmail}
                  placeholder="Enter email address"
                  keyboardType="email-address"
                  leftIcon="mail"
                />
                
                <Button
                  title={loading ? "Searching..." : "Search User"}
                  onPress={handleSearchUser}
                  disabled={loading}
                  loading={loading}
                  icon="search"
                />
              </View>
            </View>

            {/* Current Role Display */}
            {currentRole && (
              <View>
                <Text style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: theme.colors.text,
                  marginBottom: 8,
                }}>
                  Current Role
                </Text>
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                  {getCurrentRoleBadge(currentRole)}
                </View>
              </View>
            )}

            {/* Role Selection */}
            <View>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: theme.colors.text,
                marginBottom: 12,
              }}>
                Select New Role
              </Text>
              
              <View style={{ gap: 8 }}>
                {roleOptions.map((role) => (
                  <TouchableOpacity
                    key={role.value}
                    onPress={() => setSelectedRole(role.value)}
                    disabled={!canAssignRole(role.value)}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      padding: 12,
                      borderRadius: 8,
                      borderWidth: 2,
                      borderColor: selectedRole === role.value 
                        ? theme.colors.primary 
                        : theme.colors.border,
                      backgroundColor: selectedRole === role.value 
                        ? `${theme.colors.primary}10` 
                        : theme.colors.background,
                      opacity: canAssignRole(role.value) ? 1 : 0.5,
                    }}
                  >
                    <Ionicons 
                      name={role.icon as any} 
                      size={20} 
                      color={role.color} 
                      style={{ marginRight: 12 }}
                    />
                    <View style={{ flex: 1 }}>
                      <Text style={{
                        fontSize: 16,
                        fontWeight: '600',
                        color: theme.colors.text,
                      }}>
                        {role.label}
                      </Text>
                      <Text style={{
                        fontSize: 12,
                        color: theme.colors.textSecondary,
                      }}>
                        {role.description}
                      </Text>
                    </View>
                    {selectedRole === role.value && (
                      <Ionicons 
                        name="checkmark-circle" 
                        size={20} 
                        color={theme.colors.primary} 
                      />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Reason Input */}
            <View>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: theme.colors.text,
                marginBottom: 8,
              }}>
                Reason for Change *
              </Text>
              <Input
                value={reason}
                onChangeText={setReason}
                placeholder="Provide a reason for this role change..."
                multiline
                numberOfLines={3}
                style={{ height: 80 }}
              />
            </View>

            {/* Update Button */}
            <Button
              title={loading ? "Updating Role..." : "Update User Role"}
              onPress={handleRoleUpdate}
              disabled={loading || !reason.trim()}
              loading={loading}
              icon="shield"
              fullWidth
            />

            {/* Warning */}
            <View style={{
              backgroundColor: '#FEF3C7',
              borderColor: '#F59E0B',
              borderWidth: 1,
              borderRadius: 8,
              padding: 12,
              flexDirection: 'row',
              alignItems: 'flex-start',
              gap: 8,
            }}>
              <Ionicons name="warning" size={16} color="#F59E0B" />
              <Text style={{
                fontSize: 12,
                color: '#92400E',
                flex: 1,
              }}>
                Role changes are permanent and will immediately affect the user's access permissions. 
                This action will be logged for security purposes.
              </Text>
            </View>
          </View>
        </CardContent>
      </Card>
    </ScrollView>
  );
}
