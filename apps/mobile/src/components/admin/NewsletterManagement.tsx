import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert, RefreshControl } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Badge } from '../ui/Badge';
import { useTheme } from '../../providers/ThemeProvider';

interface NewsletterSubscription {
  id: string;
  email: string;
  name?: string;
  status: 'ACTIVE' | 'UNSUBSCRIBED' | 'BOUNCED';
  subscribedAt: string;
  lastEmailSent?: string;
  source: string;
  preferences: {
    weddingTips: boolean;
    vendorUpdates: boolean;
    promotions: boolean;
    events: boolean;
  };
}

interface NewsletterStats {
  totalSubscribers: number;
  activeSubscribers: number;
  unsubscribed: number;
  bounced: number;
  growthRate: number;
  openRate: number;
  clickRate: number;
}

export function NewsletterManagement() {
  const { theme } = useTheme();
  const [subscriptions, setSubscriptions] = useState<NewsletterSubscription[]>([]);
  const [stats, setStats] = useState<NewsletterStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedTab, setSelectedTab] = useState<'overview' | 'subscribers' | 'campaigns'>('overview');

  // Mock data
  const mockStats: NewsletterStats = {
    totalSubscribers: 15420,
    activeSubscribers: 14250,
    unsubscribed: 980,
    bounced: 190,
    growthRate: 12.5,
    openRate: 24.8,
    clickRate: 3.2
  };

  const mockSubscriptions: NewsletterSubscription[] = [
    {
      id: '1',
      email: '<EMAIL>',
      name: 'Priya Sharma',
      status: 'ACTIVE',
      subscribedAt: '2024-01-15T10:30:00Z',
      lastEmailSent: '2024-01-20T09:00:00Z',
      source: 'Website',
      preferences: {
        weddingTips: true,
        vendorUpdates: true,
        promotions: false,
        events: true
      }
    },
    {
      id: '2',
      email: '<EMAIL>',
      name: 'Rahul Kumar',
      status: 'ACTIVE',
      subscribedAt: '2024-01-10T14:20:00Z',
      lastEmailSent: '2024-01-20T09:00:00Z',
      source: 'Mobile App',
      preferences: {
        weddingTips: true,
        vendorUpdates: false,
        promotions: true,
        events: true
      }
    },
    {
      id: '3',
      email: '<EMAIL>',
      name: 'Anita Reddy',
      status: 'UNSUBSCRIBED',
      subscribedAt: '2023-12-05T11:15:00Z',
      source: 'Social Media',
      preferences: {
        weddingTips: false,
        vendorUpdates: false,
        promotions: false,
        events: false
      }
    }
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Simulate API calls
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStats(mockStats);
      setSubscriptions(mockSubscriptions);
    } catch (error) {
      console.error('Error loading newsletter data:', error);
      Alert.alert('Error', 'Failed to load newsletter data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleUnsubscribe = async (subscriptionId: string) => {
    Alert.alert(
      'Confirm Unsubscribe',
      'Are you sure you want to unsubscribe this user?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Unsubscribe', 
          style: 'destructive',
          onPress: () => performUnsubscribe(subscriptionId)
        }
      ]
    );
  };

  const performUnsubscribe = async (subscriptionId: string) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setSubscriptions(prev => prev.map(sub => 
        sub.id === subscriptionId 
          ? { ...sub, status: 'UNSUBSCRIBED' as const }
          : sub
      ));
      
      Alert.alert('Success', 'User has been unsubscribed');
    } catch (error) {
      Alert.alert('Error', 'Failed to unsubscribe user');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return '#10B981';
      case 'UNSUBSCRIBED': return '#6B7280';
      case 'BOUNCED': return '#EF4444';
      default: return theme.colors.textSecondary;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const filteredSubscriptions = subscriptions.filter(sub => {
    const matchesSearch = searchTerm === '' || 
      sub.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sub.name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || sub.status.toLowerCase() === statusFilter.toLowerCase();
    
    return matchesSearch && matchesStatus;
  });

  const renderStatsCard = (title: string, value: string | number, icon: string, color: string, subtitle?: string) => (
    <Card style={{ flex: 1, marginHorizontal: 4 }}>
      <CardContent style={{ padding: 16, alignItems: 'center' }}>
        <Ionicons name={icon as any} size={24} color={color} />
        <Text style={{
          fontSize: 20,
          fontWeight: '700',
          color: theme.colors.text,
          marginTop: 8,
        }}>
          {value}
        </Text>
        <Text style={{
          fontSize: 12,
          color: theme.colors.textSecondary,
          textAlign: 'center',
        }}>
          {title}
        </Text>
        {subtitle && (
          <Text style={{
            fontSize: 10,
            color: color,
            marginTop: 2,
          }}>
            {subtitle}
          </Text>
        )}
      </CardContent>
    </Card>
  );

  const renderSubscriberCard = (subscription: NewsletterSubscription) => (
    <Card key={subscription.id} style={{ marginBottom: 12 }}>
      <CardContent style={{ padding: 16 }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <View style={{ flex: 1 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginBottom: 4 }}>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: theme.colors.text,
              }}>
                {subscription.name || 'Unknown'}
              </Text>
              <Badge 
                variant="default" 
                style={{ backgroundColor: getStatusColor(subscription.status) }}
              >
                {subscription.status}
              </Badge>
            </View>
            
            <Text style={{
              fontSize: 14,
              color: theme.colors.textSecondary,
              marginBottom: 8,
            }}>
              {subscription.email}
            </Text>
            
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 16, marginBottom: 8 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                <Ionicons name="calendar" size={12} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                  {formatDate(subscription.subscribedAt)}
                </Text>
              </View>
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                <Ionicons name="location" size={12} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                  {subscription.source}
                </Text>
              </View>
            </View>
            
            {/* Preferences */}
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 4 }}>
              {Object.entries(subscription.preferences).map(([key, enabled]) => (
                enabled && (
                  <Badge key={key} variant="outline" size="sm">
                    {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                  </Badge>
                )
              ))}
            </View>
          </View>
          
          {subscription.status === 'ACTIVE' && (
            <TouchableOpacity
              onPress={() => handleUnsubscribe(subscription.id)}
              style={{
                padding: 8,
                borderRadius: 6,
                backgroundColor: theme.colors.muted,
              }}
            >
              <Ionicons name="person-remove" size={16} color="#EF4444" />
            </TouchableOpacity>
          )}
        </View>
      </CardContent>
    </Card>
  );

  const renderOverview = () => (
    <View style={{ gap: 16 }}>
      {/* Stats Grid */}
      {stats && (
        <>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            {renderStatsCard('Total', stats.totalSubscribers.toLocaleString(), 'people', '#3B82F6')}
            {renderStatsCard('Active', stats.activeSubscribers.toLocaleString(), 'checkmark-circle', '#10B981')}
          </View>
          
          <View style={{ flexDirection: 'row', gap: 8 }}>
            {renderStatsCard('Growth', `+${stats.growthRate}%`, 'trending-up', '#10B981', 'this month')}
            {renderStatsCard('Open Rate', `${stats.openRate}%`, 'mail-open', '#F59E0B')}
          </View>
        </>
      )}
      
      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <View style={{ gap: 12 }}>
            <Button
              title="Create Campaign"
              onPress={() => {/* Navigate to campaign creation */}}
              icon="add-circle"
              fullWidth
            />
            <Button
              title="Export Subscribers"
              onPress={() => {/* Handle export */}}
              variant="outline"
              icon="download"
              fullWidth
            />
            <Button
              title="View Analytics"
              onPress={() => {/* Navigate to analytics */}}
              variant="outline"
              icon="analytics"
              fullWidth
            />
          </View>
        </CardContent>
      </Card>
    </View>
  );

  const renderSubscribers = () => (
    <View style={{ gap: 16 }}>
      {/* Search and Filter */}
      <View style={{ gap: 12 }}>
        <Input
          value={searchTerm}
          onChangeText={setSearchTerm}
          placeholder="Search subscribers..."
          leftIcon="search"
        />
        
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ gap: 8 }}
        >
          {['all', 'active', 'unsubscribed', 'bounced'].map((status) => (
            <TouchableOpacity
              key={status}
              onPress={() => setStatusFilter(status)}
              style={{
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderRadius: 20,
                backgroundColor: statusFilter === status 
                  ? theme.colors.primary 
                  : theme.colors.muted,
              }}
            >
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: statusFilter === status ? '#fff' : theme.colors.text,
                textTransform: 'capitalize',
              }}>
                {status}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      
      {/* Subscribers List */}
      {filteredSubscriptions.length === 0 ? (
        <View style={{ alignItems: 'center', paddingVertical: 32 }}>
          <Ionicons name="mail-outline" size={48} color={theme.colors.textSecondary} />
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: theme.colors.text,
            marginTop: 16,
          }}>
            No Subscribers Found
          </Text>
        </View>
      ) : (
        filteredSubscriptions.map(renderSubscriberCard)
      )}
    </View>
  );

  return (
    <View style={{ flex: 1 }}>
      {/* Header */}
      <View style={{ padding: 16, paddingBottom: 8 }}>
        <Text style={{
          fontSize: 24,
          fontWeight: '700',
          color: theme.colors.text,
        }}>
          Newsletter Management
        </Text>
      </View>

      {/* Tabs */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={{ flexGrow: 0, marginBottom: 16 }}
        contentContainerStyle={{ paddingHorizontal: 16, gap: 8 }}
      >
        {[
          { key: 'overview', label: 'Overview', icon: 'analytics' },
          { key: 'subscribers', label: 'Subscribers', icon: 'people' },
          { key: 'campaigns', label: 'Campaigns', icon: 'mail' }
        ].map((tab) => (
          <TouchableOpacity
            key={tab.key}
            onPress={() => setSelectedTab(tab.key as any)}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 6,
              paddingHorizontal: 16,
              paddingVertical: 8,
              borderRadius: 20,
              backgroundColor: selectedTab === tab.key 
                ? theme.colors.primary 
                : theme.colors.muted,
            }}
          >
            <Ionicons
              name={tab.icon as any}
              size={16}
              color={selectedTab === tab.key ? '#fff' : theme.colors.textSecondary}
            />
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: selectedTab === tab.key ? '#fff' : theme.colors.text,
            }}>
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Content */}
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ padding: 16 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {loading ? (
          <View style={{ alignItems: 'center', paddingVertical: 32 }}>
            <Text style={{ color: theme.colors.textSecondary }}>Loading...</Text>
          </View>
        ) : selectedTab === 'overview' ? (
          renderOverview()
        ) : selectedTab === 'subscribers' ? (
          renderSubscribers()
        ) : (
          <View style={{ alignItems: 'center', paddingVertical: 32 }}>
            <Text style={{ color: theme.colors.textSecondary }}>Campaigns feature coming soon</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}
