import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import { useNavigation } from '@react-navigation/native';
import { Badge } from './ui';
import HamburgerMenu from './HamburgerMenu';
import type { AppStackParamList } from '../navigation/AppNavigator';
import type { StackNavigationProp } from '@react-navigation/stack';

type NavigationProp = StackNavigationProp<AppStackParamList>;

export interface HeaderProps {
  title?: string;
  showBack?: boolean;
  showProfile?: boolean;
  showNotifications?: boolean;
  showSearch?: boolean;
  showHamburger?: boolean;
  onSearchPress?: () => void;
  rightComponent?: React.ReactNode;
}

export function Header({
  title,
  showBack = false,
  showProfile = true,
  showNotifications = true,
  showSearch = true,
  showHamburger = true,
  onSearchPress,
  rightComponent,
}: HeaderProps) {
  const { theme } = useTheme();
  const { user } = useAuth();
  const navigation = useNavigation<NavigationProp>();
  const [hamburgerMenuVisible, setHamburgerMenuVisible] = useState(false);

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleProfilePress = () => {
    if (user) {
      navigation.navigate('Profile');
    } else {
      navigation.navigate('Login');
    }
  };

  const handleNotificationsPress = () => {
    navigation.navigate('Notifications');
  };

  const handleSearchPress = () => {
    if (onSearchPress) {
      onSearchPress();
    } else {
      navigation.navigate('Search');
    }
  };

  const handleHamburgerPress = () => {
    setHamburgerMenuVisible(true);
  };

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.colors.background }]}>
      <View style={[styles.container, { backgroundColor: theme.colors.background, borderBottomColor: theme.colors.border }]}>
        <View style={styles.leftSection}>
          {showBack ? (
            <TouchableOpacity onPress={handleBackPress} style={styles.iconButton}>
              <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          ) : showHamburger ? (
            <TouchableOpacity onPress={handleHamburgerPress} style={styles.iconButton}>
              <Ionicons name="menu" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          ) : (
            <View style={styles.logoSection}>
              <Text style={[styles.logoText, { color: theme.colors.primary }]}>
                Thirumanam360
              </Text>
            </View>
          )}
        </View>

        <View style={styles.centerSection}>
          {title && (
            <Text style={[styles.title, { color: theme.colors.text }]} numberOfLines={1}>
              {title}
            </Text>
          )}
        </View>

        <View style={styles.rightSection}>
          {showSearch && (
            <TouchableOpacity onPress={handleSearchPress} style={styles.iconButton}>
              <Ionicons name="search" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          )}
          
          {showNotifications && (
            <TouchableOpacity onPress={handleNotificationsPress} style={styles.iconButton}>
              <View style={styles.notificationContainer}>
                <Ionicons name="notifications-outline" size={24} color={theme.colors.text} />
                {/* Notification badge - you can add logic to show count */}
                <Badge variant="destructive" style={styles.notificationBadge}>
                  3
                </Badge>
              </View>
            </TouchableOpacity>
          )}

          {showProfile && (
            <TouchableOpacity onPress={handleProfilePress} style={styles.iconButton}>
              {user ? (
                <View style={[styles.profileAvatar, { backgroundColor: theme.colors.primary }]}>
                  <Text style={[styles.profileInitial, { color: theme.colors.primaryForeground }]}>
                    {user.fullName?.charAt(0).toUpperCase() || 'U'}
                  </Text>
                </View>
              ) : (
                <Ionicons name="person-outline" size={24} color={theme.colors.text} />
              )}
            </TouchableOpacity>
          )}

          {rightComponent}
        </View>
      </View>

      <HamburgerMenu
        visible={hamburgerMenuVisible}
        onClose={() => setHamburgerMenuVisible(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: 'white',
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    minHeight: 56,
  },
  leftSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  centerSection: {
    flex: 2,
    alignItems: 'center',
  },
  rightSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  logoSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  iconButton: {
    padding: 8,
    marginHorizontal: 4,
  },
  notificationContainer: {
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    paddingHorizontal: 4,
    paddingVertical: 0,
  },
  profileAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileInitial: {
    fontSize: 14,
    fontWeight: '600',
  },
});
