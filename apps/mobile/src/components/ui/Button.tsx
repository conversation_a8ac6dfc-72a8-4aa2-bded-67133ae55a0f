import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle, ActivityIndicator, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';

export interface ButtonProps {
  title?: string;
  onPress: () => void;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'destructive' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  children?: React.ReactNode;
  icon?: keyof typeof Ionicons.glyphMap;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  asChild?: boolean;
}

export function Button({
  title,
  onPress,
  variant = 'default',
  size = 'default',
  disabled = false,
  loading = false,
  style,
  textStyle,
  children,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  asChild = false,
}: ButtonProps) {
  const { theme } = useTheme();

  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: theme.borderRadius.md,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
    };

    // Size styles
    const sizeStyles: Record<string, ViewStyle> = {
      default: { height: 40, paddingHorizontal: 16, paddingVertical: 8 },
      sm: { height: 36, paddingHorizontal: 12, paddingVertical: 6 },
      lg: { height: 44, paddingHorizontal: 32, paddingVertical: 12 },
      icon: { height: 40, width: 40, paddingHorizontal: 0, paddingVertical: 0 },
    };

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      default: {
        backgroundColor: theme.colors.primary,
        borderWidth: 0,
      },
      outline: {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: theme.colors.border,
      },
      secondary: {
        backgroundColor: theme.colors.secondary,
        borderWidth: 0,
      },
      ghost: {
        backgroundColor: 'transparent',
        borderWidth: 0,
      },
      destructive: {
        backgroundColor: theme.colors.error,
        borderWidth: 0,
      },
      link: {
        backgroundColor: 'transparent',
        borderWidth: 0,
        paddingHorizontal: 0,
        paddingVertical: 0,
        height: 'auto',
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
      ...(fullWidth && { width: '100%' }),
      opacity: disabled || loading ? 0.6 : 1,
    };
  };

  const getTextStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      fontSize: 14,
      fontWeight: '500',
      textAlign: 'center',
    };

    // Size styles
    const sizeStyles: Record<string, TextStyle> = {
      default: { fontSize: 14 },
      sm: { fontSize: 12 },
      lg: { fontSize: 16 },
      icon: { fontSize: 14 },
    };

    // Variant styles
    const variantStyles: Record<string, TextStyle> = {
      default: { color: '#fff' },
      outline: { color: theme.colors.text },
      secondary: { color: '#fff' },
      ghost: { color: theme.colors.text },
      destructive: { color: '#fff' },
      link: {
        color: theme.colors.primary,
        textDecorationLine: 'underline',
        textDecorationColor: theme.colors.primary,
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const renderContent = () => {
    if (children) {
      return children;
    }

    const iconColor = variant === 'outline' || variant === 'ghost' || variant === 'link'
      ? theme.colors.text
      : '#fff';

    return (
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
        {loading && (
          <ActivityIndicator
            size="small"
            color={iconColor}
          />
        )}

        {!loading && icon && iconPosition === 'left' && (
          <Ionicons name={icon} size={size === 'sm' ? 16 : size === 'lg' ? 20 : 18} color={iconColor} />
        )}

        {title && (
          <Text style={[getTextStyle(), textStyle]}>
            {title}
          </Text>
        )}

        {!loading && icon && iconPosition === 'right' && (
          <Ionicons name={icon} size={size === 'sm' ? 16 : size === 'lg' ? 20 : 18} color={iconColor} />
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={variant === 'ghost' || variant === 'link' ? 0.6 : 0.8}
    >
      {renderContent()}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  // Additional styles can be added here if needed
});
