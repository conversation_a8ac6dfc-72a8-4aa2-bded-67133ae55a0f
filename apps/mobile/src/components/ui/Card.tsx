import React from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { useTheme } from '../../providers/ThemeProvider';

export interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'default' | 'elevated' | 'outlined';
}

export interface CardHeaderProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export interface CardTitleProps {
  children: React.ReactNode;
  style?: TextStyle;
}

export interface CardDescriptionProps {
  children: React.ReactNode;
  style?: TextStyle;
}

export interface CardContentProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export interface CardFooterProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export function Card({ children, style, variant = 'default' }: CardProps) {
  const { theme } = useTheme();

  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: theme.colors.card || theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      overflow: 'hidden',
    };

    const variantStyles: Record<string, ViewStyle> = {
      default: {
        borderWidth: 1,
        borderColor: theme.colors.border,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      },
      elevated: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 6,
      },
      outlined: {
        borderWidth: 1,
        borderColor: theme.colors.border,
        shadowOpacity: 0,
        elevation: 0,
      },
    };

    return {
      ...baseStyle,
      ...variantStyles[variant],
    };
  };

  return (
    <View style={[getCardStyle(), style]}>
      {children}
    </View>
  );
}

export function CardHeader({ children, style }: CardHeaderProps) {
  const { theme } = useTheme();

  const headerStyle: ViewStyle = {
    padding: theme.spacing.lg,
    paddingBottom: theme.spacing.sm,
  };

  return (
    <View style={[headerStyle, style]}>
      {children}
    </View>
  );
}

export function CardTitle({ children, style }: CardTitleProps) {
  const { theme } = useTheme();

  const titleStyle: TextStyle = {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.cardForeground,
    lineHeight: 28,
  };

  return (
    <Text style={[titleStyle, style]}>
      {children}
    </Text>
  );
}

export function CardDescription({ children, style }: CardDescriptionProps) {
  const { theme } = useTheme();

  const descriptionStyle: TextStyle = {
    fontSize: 14,
    color: theme.colors.mutedForeground,
    lineHeight: 20,
    marginTop: theme.spacing.xs,
  };

  return (
    <Text style={[descriptionStyle, style]}>
      {children}
    </Text>
  );
}

export function CardContent({ children, style }: CardContentProps) {
  const { theme } = useTheme();

  const contentStyle: ViewStyle = {
    padding: theme.spacing.lg,
    paddingTop: 0,
  };

  return (
    <View style={[contentStyle, style]}>
      {children}
    </View>
  );
}

export function CardFooter({ children, style }: CardFooterProps) {
  const { theme } = useTheme();

  const footerStyle: ViewStyle = {
    padding: theme.spacing.lg,
    paddingTop: 0,
    flexDirection: 'row',
    alignItems: 'center',
  };

  return (
    <View style={[footerStyle, style]}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  // Additional styles can be added here if needed
});
