import React from 'react';
import { View, Text, Image, TouchableOpacity, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent, CardHeader } from './ui/Card';
import { Badge } from './ui/Badge';
import { Button } from './ui/Button';
import { useTheme } from '../providers/ThemeProvider';
import { useNavigation } from '@react-navigation/native';

const { width } = Dimensions.get('window');

interface Blog {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  featuredImage?: string;
  category: string;
  publishedAt?: string;
  author?: {
    name: string;
    avatar?: string;
  };
  readTime?: number;
  views?: number;
  likes?: number;
  comments?: number;
  isPinned?: boolean;
  rating?: number;
}

interface BlogCardProps {
  blog: Blog;
  variant?: 'default' | 'featured' | 'compact';
  showAuthor?: boolean;
  onPress?: (blog: Blog) => void;
}

const BLOG_CATEGORIES: Record<string, { name: string; color: string; icon: string }> = {
  'wedding-tips': { name: 'Wedding Tips', color: '#10B981', icon: 'heart' },
  'planning': { name: 'Planning', color: '#3B82F6', icon: 'calendar' },
  'decor': { name: 'Decor', color: '#8B5CF6', icon: 'color-palette' },
  'fashion': { name: 'Fashion', color: '#EC4899', icon: 'shirt' },
  'photography': { name: 'Photography', color: '#F59E0B', icon: 'camera' },
  'venues': { name: 'Venues', color: '#EF4444', icon: 'business' },
  'catering': { name: 'Catering', color: '#06B6D4', icon: 'restaurant' },
  'music': { name: 'Music', color: '#84CC16', icon: 'musical-notes' },
};

export function BlogCard({ blog, variant = 'default', showAuthor = true, onPress }: BlogCardProps) {
  const { theme } = useTheme();
  const navigation = useNavigation();

  const category = BLOG_CATEGORIES[blog.category] || { name: blog.category, color: theme.colors.primary, icon: 'document' };
  
  const publishedDate = blog.publishedAt 
    ? new Date(blog.publishedAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    : 'Draft';

  const getReadTime = (content: string) => {
    const wordsPerMinute = 200;
    const words = content.split(' ').length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} min read`;
  };

  const handlePress = () => {
    if (onPress) {
      onPress(blog);
    } else {
      navigation.navigate('BlogDetail' as never, { blogId: blog.id } as never);
    }
  };

  if (variant === 'compact') {
    return (
      <TouchableOpacity onPress={handlePress}>
        <Card style={{ marginBottom: 12 }}>
          <CardContent style={{ padding: 16 }}>
            <View style={{ flexDirection: 'row', gap: 12 }}>
              {blog.featuredImage && (
                <Image
                  source={{ uri: blog.featuredImage }}
                  style={{
                    width: 64,
                    height: 64,
                    borderRadius: 8,
                    backgroundColor: theme.colors.muted,
                  }}
                  resizeMode="cover"
                />
              )}
              
              <View style={{ flex: 1 }}>
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginBottom: 4 }}>
                  <Badge variant="outline" size="sm">
                    {category.name}
                  </Badge>
                  {blog.isPinned && (
                    <Ionicons name="pin" size={12} color={theme.colors.primary} />
                  )}
                </View>
                
                <Text
                  style={{
                    fontSize: 14,
                    fontWeight: '600',
                    color: theme.colors.text,
                    lineHeight: 20,
                  }}
                  numberOfLines={2}
                >
                  {blog.title}
                </Text>
                
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12, marginTop: 8 }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                    <Ionicons name="time" size={12} color={theme.colors.textSecondary} />
                    <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                      {blog.readTime ? `${blog.readTime} min` : getReadTime(blog.content)}
                    </Text>
                  </View>
                  
                  {blog.views && (
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                      <Ionicons name="eye" size={12} color={theme.colors.textSecondary} />
                      <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                        {blog.views}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            </View>
          </CardContent>
        </Card>
      </TouchableOpacity>
    );
  }

  if (variant === 'featured') {
    return (
      <TouchableOpacity onPress={handlePress}>
        <Card style={{ marginBottom: 16 }}>
          {blog.featuredImage && (
            <Image
              source={{ uri: blog.featuredImage }}
              style={{
                width: '100%',
                height: 200,
                backgroundColor: theme.colors.muted,
              }}
              resizeMode="cover"
            />
          )}
          
          <CardContent style={{ padding: 16 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginBottom: 8 }}>
              <Badge variant="default" size="sm">
                {category.name}
              </Badge>
              {blog.isPinned && (
                <Ionicons name="pin" size={16} color={theme.colors.primary} />
              )}
              {blog.rating && (
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                  <Ionicons name="star" size={14} color="#F59E0B" />
                  <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                    {blog.rating.toFixed(1)}
                  </Text>
                </View>
              )}
            </View>
            
            <Text
              style={{
                fontSize: 18,
                fontWeight: '700',
                color: theme.colors.text,
                lineHeight: 24,
                marginBottom: 8,
              }}
              numberOfLines={2}
            >
              {blog.title}
            </Text>
            
            {blog.excerpt && (
              <Text
                style={{
                  fontSize: 14,
                  color: theme.colors.textSecondary,
                  lineHeight: 20,
                  marginBottom: 12,
                }}
                numberOfLines={3}
              >
                {blog.excerpt}
              </Text>
            )}
            
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12 }}>
                {showAuthor && blog.author && (
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                    {blog.author.avatar ? (
                      <Image
                        source={{ uri: blog.author.avatar }}
                        style={{ width: 24, height: 24, borderRadius: 12 }}
                      />
                    ) : (
                      <View
                        style={{
                          width: 24,
                          height: 24,
                          borderRadius: 12,
                          backgroundColor: theme.colors.primary,
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Text style={{ fontSize: 10, color: '#fff', fontWeight: '600' }}>
                          {blog.author.name.charAt(0).toUpperCase()}
                        </Text>
                      </View>
                    )}
                    <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                      {blog.author.name}
                    </Text>
                  </View>
                )}
                
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                  <Ionicons name="calendar" size={12} color={theme.colors.textSecondary} />
                  <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                    {publishedDate}
                  </Text>
                </View>
              </View>
              
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12 }}>
                {blog.likes && (
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                    <Ionicons name="heart" size={14} color="#EF4444" />
                    <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                      {blog.likes}
                    </Text>
                  </View>
                )}
                
                {blog.comments && (
                  <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                    <Ionicons name="chatbubble" size={14} color={theme.colors.textSecondary} />
                    <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                      {blog.comments}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </CardContent>
        </Card>
      </TouchableOpacity>
    );
  }

  // Default variant
  return (
    <TouchableOpacity onPress={handlePress}>
      <Card style={{ marginBottom: 16 }}>
        {blog.featuredImage && (
          <Image
            source={{ uri: blog.featuredImage }}
            style={{
              width: '100%',
              height: 160,
              backgroundColor: theme.colors.muted,
            }}
            resizeMode="cover"
          />
        )}
        
        <CardContent style={{ padding: 16 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginBottom: 8 }}>
            <Badge variant="outline" size="sm">
              {category.name}
            </Badge>
            {blog.isPinned && (
              <Ionicons name="pin" size={14} color={theme.colors.primary} />
            )}
          </View>
          
          <Text
            style={{
              fontSize: 16,
              fontWeight: '600',
              color: theme.colors.text,
              lineHeight: 22,
              marginBottom: 8,
            }}
            numberOfLines={2}
          >
            {blog.title}
          </Text>
          
          {blog.excerpt && (
            <Text
              style={{
                fontSize: 14,
                color: theme.colors.textSecondary,
                lineHeight: 20,
                marginBottom: 12,
              }}
              numberOfLines={2}
            >
              {blog.excerpt}
            </Text>
          )}
          
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                <Ionicons name="calendar" size={12} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                  {publishedDate}
                </Text>
              </View>
              
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                <Ionicons name="time" size={12} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                  {blog.readTime ? `${blog.readTime} min` : getReadTime(blog.content)}
                </Text>
              </View>
            </View>
            
            {blog.views && (
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                <Ionicons name="eye" size={12} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                  {blog.views}
                </Text>
              </View>
            )}
          </View>
        </CardContent>
      </Card>
    </TouchableOpacity>
  );
}
