import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent } from './ui/Card';
import { Button } from './ui/Button';
import { Badge } from './ui/Badge';
import { FavoriteButton } from './FavoriteButton';
import { AddToCartButton } from './AddToCartButton';
import { useTheme } from '../providers/ThemeProvider';
import { useNavigation } from '@react-navigation/native';

interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  originalPrice?: number;
  images?: string[];
  category?: string;
  rating?: number;
  reviewCount?: number;
  featured?: boolean;
  inStock?: boolean;
  discount?: number;
  vendorName?: string;
  vendorId?: string;
}

interface FeaturedShopProductsProps {
  title?: string;
  showViewAll?: boolean;
  limit?: number;
  onProductPress?: (product: Product) => void;
}

export function FeaturedShopProducts({ 
  title = "Featured Products",
  showViewAll = true,
  limit = 6,
  onProductPress
}: FeaturedShopProductsProps) {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  // Mock data for demonstration - replace with actual API call
  const mockProducts: Product[] = [
    {
      id: '1',
      name: 'Elegant Wedding Lehenga',
      description: 'Beautiful red and gold wedding lehenga with intricate embroidery',
      price: 25000,
      originalPrice: 30000,
      images: ['https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=400'],
      category: 'Bridal Wear',
      rating: 4.8,
      reviewCount: 124,
      featured: true,
      inStock: true,
      discount: 17,
      vendorName: 'Royal Bridal Collection',
      vendorId: 'vendor1'
    },
    {
      id: '2',
      name: 'Gold Jewelry Set',
      description: 'Traditional gold necklace and earring set',
      price: 45000,
      originalPrice: 50000,
      images: ['https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400'],
      category: 'Jewelry',
      rating: 4.9,
      reviewCount: 89,
      featured: true,
      inStock: true,
      discount: 10,
      vendorName: 'Golden Ornaments',
      vendorId: 'vendor2'
    },
    {
      id: '3',
      name: 'Wedding Invitation Cards',
      description: 'Premium wedding invitation cards with custom design',
      price: 150,
      images: ['https://images.unsplash.com/photo-1511285560929-80b456fea0bc?w=400'],
      category: 'Invitations',
      rating: 4.7,
      reviewCount: 56,
      featured: true,
      inStock: true,
      vendorName: 'Creative Cards',
      vendorId: 'vendor3'
    },
    {
      id: '4',
      name: 'Groom Sherwani',
      description: 'Royal blue silk sherwani with golden work',
      price: 18000,
      originalPrice: 22000,
      images: ['https://images.unsplash.com/photo-1506629905607-d9c36e0a3e3d?w=400'],
      category: 'Groom Wear',
      rating: 4.6,
      reviewCount: 78,
      featured: true,
      inStock: true,
      discount: 18,
      vendorName: 'Groom Palace',
      vendorId: 'vendor4'
    },
    {
      id: '5',
      name: 'Wedding Shoes',
      description: 'Comfortable and stylish wedding shoes for bride',
      price: 3500,
      images: ['https://images.unsplash.com/photo-1543163521-1bf539c55dd2?w=400'],
      category: 'Footwear',
      rating: 4.5,
      reviewCount: 34,
      featured: true,
      inStock: true,
      vendorName: 'Shoe Paradise',
      vendorId: 'vendor5'
    },
    {
      id: '6',
      name: 'Wedding Decorations',
      description: 'Complete wedding decoration package',
      price: 15000,
      images: ['https://images.unsplash.com/photo-1519225421980-715cb0215aed?w=400'],
      category: 'Decorations',
      rating: 4.8,
      reviewCount: 92,
      featured: true,
      inStock: true,
      vendorName: 'Decor Dreams',
      vendorId: 'vendor6'
    }
  ];

  const loadFeaturedProducts = useCallback(async () => {
    try {
      setLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Filter featured products and apply limit
      const featuredProducts = mockProducts
        .filter(product => product.featured)
        .slice(0, limit);
      
      setProducts(featuredProducts);
    } catch (error) {
      console.error('Error loading featured products:', error);
      Alert.alert('Error', 'Failed to load featured products');
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    loadFeaturedProducts();
  }, [loadFeaturedProducts]);

  const handleProductPress = (product: Product) => {
    if (onProductPress) {
      onProductPress(product);
    } else {
      navigation.navigate('ProductDetail' as never, { id: product.id } as never);
    }
  };

  const handleViewAll = () => {
    navigation.navigate('Shop' as never);
  };

  const handleImageError = (productId: string) => {
    setImageErrors(prev => new Set([...prev, productId]));
  };

  const formatPrice = (price: number) => {
    return `₹${price.toLocaleString('en-IN')}`;
  };

  const renderProductCard = (product: Product) => {
    const hasImageError = imageErrors.has(product.id);
    const imageUri = product.images?.[0];

    return (
      <TouchableOpacity
        key={product.id}
        onPress={() => handleProductPress(product)}
        style={{ width: 200, marginRight: 16 }}
      >
        <Card style={{ width: '100%' }}>
          <View style={{ position: 'relative' }}>
            {imageUri && !hasImageError ? (
              <Image
                source={{ uri: imageUri }}
                style={{
                  width: '100%',
                  height: 150,
                  borderTopLeftRadius: 8,
                  borderTopRightRadius: 8,
                }}
                onError={() => handleImageError(product.id)}
              />
            ) : (
              <View style={{
                width: '100%',
                height: 150,
                backgroundColor: theme.colors.muted,
                justifyContent: 'center',
                alignItems: 'center',
                borderTopLeftRadius: 8,
                borderTopRightRadius: 8,
              }}>
                <Ionicons name="image" size={40} color={theme.colors.textSecondary} />
              </View>
            )}

            {/* Discount Badge */}
            {product.discount && (
              <View style={{
                position: 'absolute',
                top: 8,
                left: 8,
                backgroundColor: '#DC2626',
                paddingHorizontal: 6,
                paddingVertical: 2,
                borderRadius: 4,
              }}>
                <Text style={{ color: '#fff', fontSize: 10, fontWeight: '600' }}>
                  {product.discount}% OFF
                </Text>
              </View>
            )}

            {/* Favorite Button */}
            <View style={{ position: 'absolute', top: 8, right: 8 }}>
              <FavoriteButton
                itemId={product.id}
                itemType="PRODUCT"
                size={20}
              />
            </View>

            {/* Stock Status */}
            {!product.inStock && (
              <View style={{
                position: 'absolute',
                bottom: 8,
                left: 8,
                backgroundColor: 'rgba(0,0,0,0.7)',
                paddingHorizontal: 6,
                paddingVertical: 2,
                borderRadius: 4,
              }}>
                <Text style={{ color: '#fff', fontSize: 10 }}>Out of Stock</Text>
              </View>
            )}
          </View>

          <CardContent style={{ padding: 12 }}>
            <View style={{ gap: 8 }}>
              {/* Category */}
              {product.category && (
                <Badge variant="outline" size="sm">
                  {product.category}
                </Badge>
              )}

              {/* Product Name */}
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: theme.colors.text,
                numberOfLines: 2,
              }} numberOfLines={2}>
                {product.name}
              </Text>

              {/* Vendor Name */}
              {product.vendorName && (
                <Text style={{
                  fontSize: 12,
                  color: theme.colors.textSecondary,
                  numberOfLines: 1,
                }} numberOfLines={1}>
                  by {product.vendorName}
                </Text>
              )}

              {/* Rating */}
              {product.rating && (
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                  <Ionicons name="star" size={12} color="#F59E0B" />
                  <Text style={{ fontSize: 12, color: theme.colors.text }}>
                    {product.rating.toFixed(1)}
                  </Text>
                  {product.reviewCount && (
                    <Text style={{ fontSize: 10, color: theme.colors.textSecondary }}>
                      ({product.reviewCount})
                    </Text>
                  )}
                </View>
              )}

              {/* Price */}
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Text style={{
                  fontSize: 16,
                  fontWeight: '700',
                  color: theme.colors.primary,
                }}>
                  {formatPrice(product.price)}
                </Text>
                {product.originalPrice && product.originalPrice > product.price && (
                  <Text style={{
                    fontSize: 12,
                    color: theme.colors.textSecondary,
                    textDecorationLine: 'line-through',
                  }}>
                    {formatPrice(product.originalPrice)}
                  </Text>
                )}
              </View>

              {/* Add to Cart Button */}
              <AddToCartButton
                product={{
                  id: product.id,
                  name: product.name,
                  price: product.price,
                  image: product.images?.[0],
                  vendorId: product.vendorId,
                  vendorName: product.vendorName,
                  inStock: product.inStock
                }}
                size="sm"
                fullWidth
              />
            </View>
          </CardContent>
        </Card>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={{ padding: 16 }}>
        <Text style={{ 
          fontSize: 18, 
          fontWeight: '700', 
          color: theme.colors.text,
          marginBottom: 16 
        }}>
          {title}
        </Text>
        <View style={{ flexDirection: 'row', gap: 16 }}>
          {[1, 2, 3].map((i) => (
            <View key={i} style={{
              width: 200,
              height: 250,
              backgroundColor: theme.colors.muted,
              borderRadius: 8,
            }} />
          ))}
        </View>
      </View>
    );
  }

  if (products.length === 0) {
    return (
      <View style={{ padding: 16, alignItems: 'center' }}>
        <Text style={{ 
          fontSize: 18, 
          fontWeight: '700', 
          color: theme.colors.text,
          marginBottom: 8 
        }}>
          {title}
        </Text>
        <Text style={{ color: theme.colors.textSecondary }}>
          No featured products available
        </Text>
      </View>
    );
  }

  return (
    <View style={{ paddingVertical: 16 }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 16,
        marginBottom: 16,
      }}>
        <Text style={{
          fontSize: 18,
          fontWeight: '700',
          color: theme.colors.text,
        }}>
          {title}
        </Text>
        {showViewAll && (
          <Button
            title="View All"
            onPress={handleViewAll}
            variant="ghost"
            size="sm"
            icon="arrow-forward"
            iconPosition="right"
          />
        )}
      </View>

      {/* Products Carousel */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 16 }}
        style={{ flexGrow: 0 }}
      >
        {products.map(renderProductCard)}
      </ScrollView>
    </View>
  );
}
