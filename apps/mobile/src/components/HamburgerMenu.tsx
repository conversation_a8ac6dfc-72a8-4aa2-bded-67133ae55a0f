import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  SafeAreaView,
  Dimensions,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import { useCart } from '../providers/CartProvider';
import { useFavorites } from '../providers/FavoritesProvider';
import { getUserRole } from './RoleBasedAccess';

const { width } = Dimensions.get('window');

interface HamburgerMenuProps {
  visible: boolean;
  onClose: () => void;
}

interface MenuItem {
  id: string;
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  screen?: string;
  params?: any;
  children?: MenuItem[];
  requiresAuth?: boolean;
  roles?: string[];
}

export default function HamburgerMenu({ visible, onClose }: HamburgerMenuProps) {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user, isAuthenticated, logout } = useAuth();
  const { clearCart } = useCart();
  const { clearFavorites } = useFavorites();

  const userRole = getUserRole(user);
  const isVendor = userRole === 'vendor' || userRole === 'admin' || userRole === 'super_admin';
  const isAdmin = userRole === 'admin' || userRole === 'super_admin';

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await clearCart();
              await clearFavorites();
              await logout();
              onClose();
              // Navigation will be handled automatically by auth state change
            } catch (error) {
              console.error('Sign out error:', error);
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          },
        },
      ]
    );
  };

  const menuItems: MenuItem[] = [
    {
      id: 'home',
      title: 'Home',
      icon: 'home-outline',
      screen: 'Home',
    },
    {
      id: 'vendors',
      title: 'Vendors',
      icon: 'people-outline',
      children: [
        { id: 'all-vendors', title: 'All Vendors', icon: 'list-outline', screen: 'Vendors' },
        { id: 'photographers', title: 'Photographers', icon: 'camera-outline', screen: 'Vendors', params: { category: 'photography' } },
        { id: 'decorators', title: 'Decorators', icon: 'flower-outline', screen: 'Vendors', params: { category: 'decoration' } },
        { id: 'caterers', title: 'Caterers', icon: 'restaurant-outline', screen: 'Vendors', params: { category: 'catering' } },
        { id: 'makeup', title: 'Makeup Artists', icon: 'brush-outline', screen: 'Vendors', params: { category: 'makeup' } },
        { id: 'music', title: 'Music & DJ', icon: 'musical-notes-outline', screen: 'Vendors', params: { category: 'music' } },
      ],
    },
    {
      id: 'venues',
      title: 'Venues',
      icon: 'business-outline',
      children: [
        { id: 'all-venues', title: 'All Venues', icon: 'list-outline', screen: 'Venues' },
        { id: 'banquet-halls', title: 'Banquet Halls', icon: 'business-outline', screen: 'Venues', params: { category: 'banquet' } },
        { id: 'hotels', title: 'Hotels', icon: 'bed-outline', screen: 'Venues', params: { category: 'hotel' } },
        { id: 'resorts', title: 'Resorts', icon: 'leaf-outline', screen: 'Venues', params: { category: 'resort' } },
        { id: 'outdoor', title: 'Outdoor Venues', icon: 'sunny-outline', screen: 'Venues', params: { category: 'outdoor' } },
      ],
    },
    {
      id: 'shop',
      title: 'Shop',
      icon: 'storefront-outline',
      children: [
        { id: 'all-products', title: 'All Products', icon: 'list-outline', screen: 'Shop' },
        { id: 'bridal-wear', title: 'Bridal Wear', icon: 'shirt-outline', screen: 'Shop', params: { category: 'bridal-wear' } },
        { id: 'groom-wear', title: 'Groom Wear', icon: 'man-outline', screen: 'Shop', params: { category: 'groom-wear' } },
        { id: 'jewelry', title: 'Jewelry', icon: 'diamond-outline', screen: 'Shop', params: { category: 'jewelry' } },
        { id: 'accessories', title: 'Accessories', icon: 'bag-outline', screen: 'Shop', params: { category: 'accessories' } },
        { id: 'gifts', title: 'Wedding Gifts', icon: 'gift-outline', screen: 'Shop', params: { category: 'gifts' } },
      ],
    },
    {
      id: 'planning',
      title: 'Planning Tools',
      icon: 'calendar-outline',
      children: [
        { id: 'wedding-planning', title: 'Wedding Planning', icon: 'calendar-outline', screen: 'WeddingPlanning' },
        { id: 'budget-tracker', title: 'Budget Tracker', icon: 'calculator-outline', screen: 'BudgetTracker' },
        { id: 'guest-list', title: 'Guest List Manager', icon: 'people-outline', screen: 'GuestListManager' },
        { id: 'timeline', title: 'Timeline', icon: 'time-outline', screen: 'Timeline' },
      ],
    },
    {
      id: 'content',
      title: 'Content',
      icon: 'library-outline',
      children: [
        { id: 'blog', title: 'Blog', icon: 'document-text-outline', screen: 'Blog' },
        { id: 'photos', title: 'Photos', icon: 'images-outline', screen: 'Photos' },
        { id: 'real-weddings', title: 'Real Weddings', icon: 'heart-outline', screen: 'RealWeddings' },
        { id: 'community', title: 'Community', icon: 'chatbubbles-outline', screen: 'Community' },
      ],
    },
  ];

  // Add authenticated user menu items
  if (isAuthenticated) {
    menuItems.push({
      id: 'dashboard',
      title: 'Dashboard',
      icon: 'grid-outline',
      children: [
        { id: 'customer-dashboard', title: 'My Dashboard', icon: 'person-outline', screen: 'CustomerDashboard' },
        { id: 'orders', title: 'My Orders', icon: 'bag-outline', screen: 'Orders' },
        { id: 'bookings', title: 'My Bookings', icon: 'calendar-outline', screen: 'BookingHistory' },
        { id: 'favorites', title: 'Favorites', icon: 'heart-outline', screen: 'Favorites' },
      ],
    });

    // Add vendor menu items
    if (isVendor) {
      menuItems.push({
        id: 'vendor-dashboard',
        title: 'Vendor Dashboard',
        icon: 'business-outline',
        children: [
          { id: 'vendor-profile', title: 'Business Profile', icon: 'business-outline', screen: 'VendorBusinessProfile' },
          { id: 'vendor-shop', title: 'Shop Management', icon: 'storefront-outline', screen: 'VendorShopManagement' },
          { id: 'vendor-inquiries', title: 'Inquiries', icon: 'mail-outline', screen: 'VendorInquiryManagement' },
          { id: 'vendor-analytics', title: 'Analytics', icon: 'analytics-outline', screen: 'VendorAnalytics' },
        ],
      });
    }

    // Add admin menu items
    if (isAdmin) {
      menuItems.push({
        id: 'admin-dashboard',
        title: 'Admin Dashboard',
        icon: 'shield-outline',
        children: [
          { id: 'admin-users', title: 'User Management', icon: 'people-outline', screen: 'AdminUserManagement' },
          { id: 'admin-orders', title: 'Order Management', icon: 'bag-outline', screen: 'AdminOrderManagement' },
          { id: 'admin-newsletter', title: 'Newsletter', icon: 'mail-outline', screen: 'AdminNewsletterManagement' },
          { id: 'admin-analytics', title: 'Analytics', icon: 'analytics-outline', screen: 'AdminAnalytics' },
        ],
      });
    }
  }

  // Add support menu items
  menuItems.push({
    id: 'support',
    title: 'Support',
    icon: 'help-circle-outline',
    children: [
      { id: 'help', title: 'Help Center', icon: 'help-circle-outline', screen: 'Help' },
      { id: 'contact', title: 'Contact Us', icon: 'call-outline', screen: 'Contact' },
      { id: 'privacy', title: 'Privacy Policy', icon: 'shield-outline', screen: 'Privacy' },
      { id: 'terms', title: 'Terms of Service', icon: 'document-text-outline', screen: 'Terms' },
    ],
  });

  // Add signout option for authenticated users
  if (isAuthenticated) {
    menuItems.push({
      id: 'signout',
      title: 'Sign Out',
      icon: 'log-out-outline',
      requiresAuth: true,
    });
  }

  const handleMenuItemPress = (item: MenuItem) => {
    if (item.id === 'signout') {
      handleSignOut();
    } else if (item.screen) {
      navigation.navigate(item.screen as never, item.params as never);
      onClose();
    }
  };

  const renderMenuItem = (item: MenuItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const paddingLeft = 20 + (level * 20);

    if (hasChildren) {
      return (
        <View key={item.id}>
          <View style={[styles.menuSection, { paddingLeft }]}>
            <Ionicons name={item.icon} size={20} color={theme.colors.primary} />
            <Text style={[styles.menuSectionTitle, { color: theme.colors.text }]}>
              {item.title}
            </Text>
          </View>
          {item.children?.map(child => renderMenuItem(child, level + 1))}
        </View>
      );
    }

    // Special styling for signout button
    const isSignOut = item.id === 'signout';
    const buttonStyle = isSignOut
      ? [styles.menuItem, styles.signOutItem, { paddingLeft, backgroundColor: theme.colors.error + '10' }]
      : [styles.menuItem, { paddingLeft }];
    const iconColor = isSignOut ? theme.colors.error : theme.colors.textSecondary;
    const textColor = isSignOut ? theme.colors.error : theme.colors.text;

    return (
      <TouchableOpacity
        key={item.id}
        style={buttonStyle}
        onPress={() => handleMenuItemPress(item)}
      >
        <Ionicons name={item.icon} size={18} color={iconColor} />
        <Text style={[styles.menuItemText, { color: textColor }]}>
          {item.title}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity style={styles.overlayTouch} onPress={onClose} />
        <SafeAreaView style={[styles.menuContainer, { backgroundColor: theme.colors.surface }]}>
          <View style={[styles.header, { borderBottomColor: theme.colors.border }]}>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
              Menu
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.menuContent} showsVerticalScrollIndicator={false}>
            {menuItems.map(item => renderMenuItem(item))}
          </ScrollView>
        </SafeAreaView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    flexDirection: 'row',
  },
  overlayTouch: {
    flex: 1,
  },
  menuContainer: {
    width: width * 0.8,
    maxWidth: 320,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 5,
  },
  menuContent: {
    flex: 1,
    paddingVertical: 10,
  },
  menuSection: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  menuSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  menuItemText: {
    fontSize: 14,
    marginLeft: 12,
  },
  signOutItem: {
    marginTop: 10,
    marginHorizontal: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
});
