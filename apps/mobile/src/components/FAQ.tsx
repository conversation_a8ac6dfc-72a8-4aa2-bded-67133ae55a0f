import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Input } from './ui/Input';
import { Badge } from './ui/Badge';
import { useTheme } from '../providers/ThemeProvider';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
  popular?: boolean;
}

interface FAQProps {
  showSearch?: boolean;
  showCategories?: boolean;
  limit?: number;
  category?: string;
}

export function FAQ({ showSearch = true, showCategories = true, limit, category }: FAQProps) {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(category || 'all');
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const categories = [
    { id: 'all', name: 'All Questions', icon: 'help-circle' },
    { id: 'account', name: 'Account & Profile', icon: 'person' },
    { id: 'vendors', name: 'Finding Vendors', icon: 'search' },
    { id: 'venues', name: 'Booking Venues', icon: 'location' },
    { id: 'shopping', name: 'Shopping', icon: 'bag' },
    { id: 'planning', name: 'Wedding Planning', icon: 'calendar' },
    { id: 'reviews', name: 'Reviews & Ratings', icon: 'star' },
    { id: 'payments', name: 'Payments & Billing', icon: 'card' },
    { id: 'support', name: 'Customer Support', icon: 'call' }
  ];

  const faqData: FAQItem[] = [
    {
      id: '1',
      question: 'How do I create an account on Thirumanam360?',
      answer: 'To create an account, tap on the "Sign Up" button on the login screen. You can register using your email address or mobile number. Fill in your basic details, verify your email/phone, and your account will be ready to use.',
      category: 'account',
      tags: ['signup', 'registration', 'account'],
      popular: true
    },
    {
      id: '2',
      question: 'How can I find vendors in my city?',
      answer: 'Use the search feature on the Vendors page. You can filter by location, category (photographer, decorator, caterer, etc.), budget range, and ratings. You can also browse by popular vendors in your area.',
      category: 'vendors',
      tags: ['search', 'location', 'vendors'],
      popular: true
    },
    {
      id: '3',
      question: 'How do I book a venue for my wedding?',
      answer: 'Browse venues on the Venues page, use filters to find venues that match your requirements. Click on a venue to view details, check availability, and send an inquiry. The venue owner will respond with availability and pricing.',
      category: 'venues',
      tags: ['booking', 'venues', 'availability'],
      popular: true
    },
    {
      id: '4',
      question: 'Is it safe to make payments through the app?',
      answer: 'Yes, all payments are processed through secure payment gateways with SSL encryption. We support multiple payment methods including UPI, credit/debit cards, and net banking. Your payment information is never stored on our servers.',
      category: 'payments',
      tags: ['security', 'payments', 'safety'],
      popular: true
    },
    {
      id: '5',
      question: 'How can I track my orders?',
      answer: 'Go to your Profile > Order History to view all your orders. Each order shows the current status, tracking information, and expected delivery date. You\'ll also receive notifications for status updates.',
      category: 'shopping',
      tags: ['orders', 'tracking', 'delivery']
    },
    {
      id: '6',
      question: 'Can I cancel or modify my booking?',
      answer: 'Cancellation and modification policies vary by vendor. Check the specific vendor\'s policy on their profile page. Generally, you can cancel or modify bookings up to a certain time before the event date.',
      category: 'venues',
      tags: ['cancellation', 'modification', 'policy']
    },
    {
      id: '7',
      question: 'How do I write a review for a vendor?',
      answer: 'After your event or service completion, go to the vendor\'s profile and click "Write Review". You can rate different aspects like service quality, value for money, and professionalism. Your review helps other couples make informed decisions.',
      category: 'reviews',
      tags: ['reviews', 'ratings', 'feedback']
    },
    {
      id: '8',
      question: 'What if I face issues with a vendor?',
      answer: 'Contact our customer support team through the app or call our helpline. We have a dedicated team to resolve disputes and ensure quality service. You can also report issues directly from the vendor\'s profile page.',
      category: 'support',
      tags: ['issues', 'disputes', 'support']
    },
    {
      id: '9',
      question: 'How do I use the wedding planning tools?',
      answer: 'Access planning tools from your Profile menu. You can create guest lists, manage budgets, set timelines, and track tasks. All your planning data is saved and synced across devices.',
      category: 'planning',
      tags: ['planning', 'tools', 'budget', 'timeline']
    },
    {
      id: '10',
      question: 'Can I get refunds for cancelled orders?',
      answer: 'Refund policies depend on the vendor and timing of cancellation. Most vendors offer full refunds if cancelled within 24-48 hours. Check the specific refund policy before making a booking.',
      category: 'payments',
      tags: ['refunds', 'cancellation', 'policy']
    }
  ];

  const filteredFAQs = faqData.filter(faq => {
    const matchesSearch = searchQuery === '' || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  }).slice(0, limit);

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const renderFAQItem = (faq: FAQItem) => (
    <Card key={faq.id} style={{ marginBottom: 12 }}>
      <TouchableOpacity onPress={() => toggleExpanded(faq.id)}>
        <CardContent style={{ padding: 16 }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <View style={{ flex: 1, marginRight: 12 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginBottom: 4 }}>
                <Text style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: theme.colors.text,
                  flex: 1,
                }}>
                  {faq.question}
                </Text>
                {faq.popular && (
                  <Badge variant="secondary" size="sm">
                    Popular
                  </Badge>
                )}
              </View>
              
              {expandedItems.includes(faq.id) && (
                <View style={{ marginTop: 12 }}>
                  <Text style={{
                    fontSize: 14,
                    color: theme.colors.textSecondary,
                    lineHeight: 20,
                  }}>
                    {faq.answer}
                  </Text>
                  
                  <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 4, marginTop: 8 }}>
                    {faq.tags.map((tag) => (
                      <Badge key={tag} variant="outline" size="sm">
                        {tag}
                      </Badge>
                    ))}
                  </View>
                </View>
              )}
            </View>
            
            <Ionicons
              name={expandedItems.includes(faq.id) ? "chevron-up" : "chevron-down"}
              size={20}
              color={theme.colors.textSecondary}
            />
          </View>
        </CardContent>
      </TouchableOpacity>
    </Card>
  );

  return (
    <View style={{ flex: 1 }}>
      {/* Header */}
      <View style={{ padding: 16, paddingBottom: 8 }}>
        <Text style={{
          fontSize: 24,
          fontWeight: '700',
          color: theme.colors.text,
          marginBottom: 8,
        }}>
          Frequently Asked Questions
        </Text>
        <Text style={{
          fontSize: 14,
          color: theme.colors.textSecondary,
        }}>
          Find answers to common questions about Thirumanam360
        </Text>
      </View>

      {/* Search */}
      {showSearch && (
        <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
          <Input
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search FAQs..."
            leftIcon="search"
          />
        </View>
      )}

      {/* Categories */}
      {showCategories && (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={{ flexGrow: 0, marginBottom: 16 }}
          contentContainerStyle={{ paddingHorizontal: 16, gap: 8 }}
        >
          {categories.map((cat) => (
            <TouchableOpacity
              key={cat.id}
              onPress={() => setSelectedCategory(cat.id)}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 6,
                paddingHorizontal: 12,
                paddingVertical: 8,
                borderRadius: 20,
                backgroundColor: selectedCategory === cat.id 
                  ? theme.colors.primary 
                  : theme.colors.muted,
              }}
            >
              <Ionicons
                name={cat.icon as any}
                size={16}
                color={selectedCategory === cat.id ? '#fff' : theme.colors.textSecondary}
              />
              <Text style={{
                fontSize: 12,
                fontWeight: '600',
                color: selectedCategory === cat.id ? '#fff' : theme.colors.text,
              }}>
                {cat.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}

      {/* FAQ List */}
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ padding: 16 }}
        showsVerticalScrollIndicator={false}
      >
        {filteredFAQs.length === 0 ? (
          <View style={{ alignItems: 'center', paddingVertical: 32 }}>
            <Ionicons name="help-circle-outline" size={48} color={theme.colors.textSecondary} />
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: theme.colors.text,
              marginTop: 16,
            }}>
              No FAQs Found
            </Text>
            <Text style={{
              fontSize: 14,
              color: theme.colors.textSecondary,
              textAlign: 'center',
              marginTop: 4,
            }}>
              Try adjusting your search or category filter
            </Text>
          </View>
        ) : (
          filteredFAQs.map(renderFAQItem)
        )}
      </ScrollView>
    </View>
  );
}
