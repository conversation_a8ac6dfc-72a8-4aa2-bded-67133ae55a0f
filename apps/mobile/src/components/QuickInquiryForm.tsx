import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Button } from './ui/Button';
import { Input } from './ui/Input';
import { Badge } from './ui/Badge';
import { useAuth } from '../providers/AuthProvider';
import { useTheme } from '../providers/ThemeProvider';

interface QuickInquiryFormProps {
  vendorUserId: string;
  vendorId: string;
  vendorName: string;
  vendorCategory?: string;
  onSubmit?: (data: any) => void;
  onClose?: () => void;
}

export function QuickInquiryForm({ 
  vendorUserId, 
  vendorId, 
  vendorName, 
  vendorCategory,
  onSubmit,
  onClose 
}: QuickInquiryFormProps) {
  const { user, isAuthenticated, userProfile } = useAuth();
  const { theme } = useTheme();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    eventDate: '',
    message: '',
    inquiryType: 'VENDOR_INQUIRY' as const,
    budget: '',
    guestCount: '',
    venue: '',
    preferredContactTime: ''
  });

  // Pre-fill form with user profile data when authenticated
  useEffect(() => {
    if (isAuthenticated && userProfile) {
      setFormData(prev => ({
        ...prev,
        customerName: userProfile.fullName || userProfile.firstName || '',
        customerEmail: userProfile.email || '',
        customerPhone: userProfile.phone || '',
      }));
    }
  }, [isAuthenticated, userProfile]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = () => {
    const required = ['customerName', 'customerEmail', 'customerPhone', 'message'];
    const missing = required.filter(field => !formData[field as keyof typeof formData]);
    
    if (missing.length > 0) {
      Alert.alert('Validation Error', `Please fill in: ${missing.join(', ')}`);
      return false;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.customerEmail)) {
      Alert.alert('Validation Error', 'Please enter a valid email address');
      return false;
    }

    // Phone validation
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(formData.customerPhone.replace(/\s/g, ''))) {
      Alert.alert('Validation Error', 'Please enter a valid phone number');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setIsSubmitting(true);

      const inquiryData = {
        ...formData,
        vendorUserId,
        vendorId,
        vendorName,
        vendorCategory: vendorCategory || 'General',
        customerUserId: user?.id || null,
        status: 'PENDING',
        priority: 'MEDIUM',
        source: 'MOBILE_APP',
        metadata: {
          submittedAt: new Date().toISOString(),
          userAgent: 'Mobile App',
          platform: 'React Native'
        }
      };

      // Here you would typically call your inquiry service
      // await inquiryService.createInquiry(inquiryData);
      
      onSubmit?.(inquiryData);
      setIsSubmitted(true);
      
      Alert.alert(
        'Success!', 
        'Your inquiry has been sent successfully. The vendor will contact you soon.',
        [
          { text: 'OK', onPress: () => onClose?.() }
        ]
      );

    } catch (error) {
      console.error('Error submitting inquiry:', error);
      Alert.alert('Error', 'Failed to send inquiry. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <Card style={{ margin: 16 }}>
        <CardContent style={{ alignItems: 'center', padding: 24 }}>
          <Ionicons name="checkmark-circle" size={64} color="#10B981" />
          <Text style={{ 
            fontSize: 18, 
            fontWeight: '600', 
            color: theme.colors.text,
            marginTop: 16,
            textAlign: 'center'
          }}>
            Inquiry Sent Successfully!
          </Text>
          <Text style={{ 
            fontSize: 14, 
            color: theme.colors.textSecondary,
            marginTop: 8,
            textAlign: 'center'
          }}>
            {vendorName} will contact you soon.
          </Text>
          <Button
            title="Close"
            onPress={onClose}
            variant="outline"
            style={{ marginTop: 16 }}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <ScrollView style={{ flex: 1 }}>
      <Card style={{ margin: 16 }}>
        <CardHeader>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <Ionicons name="chatbubble-ellipses" size={24} color={theme.colors.primary} />
            <CardTitle>Quick Inquiry</CardTitle>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginTop: 8 }}>
            <Text style={{ fontSize: 14, color: theme.colors.textSecondary }}>
              Send inquiry to:
            </Text>
            <Badge variant="default" size="sm">
              {vendorName}
            </Badge>
          </View>
        </CardHeader>

        <CardContent>
          <View style={{ gap: 16 }}>
            <Input
              label="Your Name *"
              value={formData.customerName}
              onChangeText={(value) => handleInputChange('customerName', value)}
              placeholder="Enter your full name"
              leftIcon="person"
            />

            <Input
              label="Email Address *"
              value={formData.customerEmail}
              onChangeText={(value) => handleInputChange('customerEmail', value)}
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              leftIcon="mail"
            />

            <Input
              label="Phone Number *"
              value={formData.customerPhone}
              onChangeText={(value) => handleInputChange('customerPhone', value)}
              placeholder="Enter your phone number"
              keyboardType="phone-pad"
              leftIcon="call"
            />

            <Input
              label="Event Date"
              value={formData.eventDate}
              onChangeText={(value) => handleInputChange('eventDate', value)}
              placeholder="DD/MM/YYYY"
              leftIcon="calendar"
            />

            <Input
              label="Guest Count"
              value={formData.guestCount}
              onChangeText={(value) => handleInputChange('guestCount', value)}
              placeholder="Approximate number of guests"
              keyboardType="numeric"
              leftIcon="people"
            />

            <Input
              label="Budget Range"
              value={formData.budget}
              onChangeText={(value) => handleInputChange('budget', value)}
              placeholder="Your budget range"
              leftIcon="card"
            />

            <Input
              label="Message *"
              value={formData.message}
              onChangeText={(value) => handleInputChange('message', value)}
              placeholder="Describe your requirements..."
              multiline
              numberOfLines={4}
              style={{ height: 100, textAlignVertical: 'top' }}
              leftIcon="chatbubble"
            />

            <Input
              label="Preferred Contact Time"
              value={formData.preferredContactTime}
              onChangeText={(value) => handleInputChange('preferredContactTime', value)}
              placeholder="Best time to contact you"
              leftIcon="time"
            />

            <View style={{ flexDirection: 'row', gap: 12, marginTop: 8 }}>
              <Button
                title="Cancel"
                onPress={onClose}
                variant="outline"
                style={{ flex: 1 }}
              />
              <Button
                title={isSubmitting ? "Sending..." : "Send Inquiry"}
                onPress={handleSubmit}
                disabled={isSubmitting}
                loading={isSubmitting}
                style={{ flex: 1 }}
                icon="send"
                iconPosition="right"
              />
            </View>
          </View>
        </CardContent>
      </Card>
    </ScrollView>
  );
}
