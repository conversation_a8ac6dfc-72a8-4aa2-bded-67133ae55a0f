'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { MapPin, Calendar, X } from "lucide-react"
import { LayoutWrapper } from "@/components/layout-wrapper"
import { useRouter } from "next/navigation"
import { useState, useRef } from "react"
import { useSafeTranslation } from '@/hooks/use-safe-translation'
import { useStateContext, INDIAN_STATES } from '@/contexts/StateContext'
import { FeaturedShopProducts } from '@/components/FeaturedShopProducts'
import { Carousel } from '@/components/ui/carousel'
import { SimpleSEO } from '@/components/seo/SimpleSEO'
import { PopularDestinationsAd } from '@/components/PopularDestinationsAd'
import NewsletterSubscription from '@/components/newsletter/NewsletterSubscription'

export default function MobileHomePage() {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { selectedState } = useStateContext();

  // Search state
  const [locationInput, setLocationInput] = useState("");
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false);
  const [filteredCities, setFilteredCities] = useState<Array<{name: string, state: string}>>([]);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [isSearching, setIsSearching] = useState(false);
  const locationInputRef = useRef<HTMLInputElement>(null);

  // Create a flat list of all cities for searching
  const allCities = INDIAN_STATES.flatMap(state =>
    state.cities.map(city => ({
      name: t(city.nameKey, city.nameKey.split('.').pop()),
      state: state.name
    }))
  );

  // Handle location input change and filtering
  const handleLocationInputChange = (value: string) => {
    setLocationInput(value);

    if (value.length > 0) {
      const filtered = allCities.filter(city =>
        city.name.toLowerCase().includes(value.toLowerCase()) ||
        city.state.toLowerCase().includes(value.toLowerCase())
      ).slice(0, 8); // Limit to 8 suggestions

      setFilteredCities(filtered);
      setShowLocationSuggestions(true);
    } else {
      setFilteredCities([]);
      setShowLocationSuggestions(false);
    }
  };

  // Handle location selection
  const handleLocationSelect = (cityName: string) => {
    setLocationInput(cityName);
    setShowLocationSuggestions(false);
  };

  // Handle search
  const handleSearch = () => {
    if (!locationInput.trim()) {
      alert(t('search.locationRequired', 'Please enter a location'));
      return;
    }

    setIsSearching(true);
    
    // Build search URL with parameters
    const searchParams = new URLSearchParams();
    searchParams.set('city', locationInput);
    
    if (selectedDate) {
      searchParams.set('date', selectedDate.toISOString().split('T')[0]);
    }

    // Navigate to vendors page with search parameters
    router.push(`/vendors?${searchParams.toString()}`);
    
    setTimeout(() => setIsSearching(false), 1000);
  };

  return (
    <>
      <SimpleSEO
        title="Thirumanam 360 Mobile - Your Dream Wedding Starts Here"
        description="Discover the best wedding vendors, venues, and inspiration for your perfect day. Plan your dream wedding with Thirumanam 360 - India's premier wedding planning platform."
        keywords="wedding planning, wedding vendors, wedding venues, Tamil wedding, Indian wedding, marriage planning, wedding services, wedding marketplace"
        image="/hero_image_1.webp"
        url="/mobile-home"
      />
      <LayoutWrapper>
        <div className="min-h-screen bg-white">
          {/* Hero Section */}
          <section className="relative overflow-hidden">
            <div className="relative w-full h-64">
              <Carousel
                images={[
                  {
                    src: "/hero_image_1.webp",
                    alt: "Beautiful wedding celebration with traditional Tamil Nadu elements"
                  },
                  {
                    src: "/hero_image_2.webp",
                    alt: "Elegant wedding venue with traditional decorations"
                  },
                  {
                    src: "/hero_image_3.webp",
                    alt: "Traditional wedding ceremony with cultural elements"
                  }
                ]}
                autoPlayInterval={3000}
                showArrows={false}
                showDots={false}
                className="w-full h-full"
              />
            </div>
            
            {/* Hero Content Overlay */}
            <div className="absolute inset-0 z-20 flex items-center justify-center">
              <div className="text-center px-4">
                <h1 className="text-2xl font-bold text-white mb-4 drop-shadow-lg">
                  {t('hero.title', 'Plan Your Dream Wedding With Us!')}
                </h1>
              </div>
            </div>
          </section>

          {/* Mobile Filter Bar - Below Hero Section */}
          <div className="w-full bg-white z-20 px-2 py-3 flex flex-col items-center border-b border-gray-100">
            <form onSubmit={(e) => { e.preventDefault(); handleSearch(); }} className="flex flex-col gap-2 w-full max-w-xs mx-auto">
              <div className="flex flex-row gap-2 w-full">
                <div className="relative w-1/2">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    type="text"
                    placeholder={t('hero.locationPlaceholder', 'Select City')}
                    value={locationInput}
                    onChange={e => handleLocationInputChange(e.target.value)}
                    className="pl-10 h-9 text-sm border-2 border-primary/30 bg-white rounded-lg"
                    autoComplete="off"
                  />
                </div>
                <div className="relative w-1/2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full h-9 pl-10 pr-8 text-left font-normal justify-start text-sm border-2 border-primary/30 bg-white rounded-lg"
                      >
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        {selectedDate ? (
                          <>
                            {selectedDate.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })}
                            <X className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 hover:text-gray-600 cursor-pointer" onClick={e => { e.stopPropagation(); setSelectedDate(undefined); }} />
                          </>
                        ) : (
                          <span>{t('hero.datePlaceholder', 'Select Date')}</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <CalendarComponent mode="single" selected={selectedDate} onSelect={setSelectedDate} disabled={date => date < new Date(new Date().setHours(0, 0, 0, 0))} initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <Button
                type="submit"
                className="h-9 w-full bg-[#5d1417] text-white text-sm font-semibold rounded-lg"
                onClick={handleSearch}
                disabled={isSearching}
              >
                {isSearching ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Searching...
                  </>
                ) : (
                  t('hero.searchButton', 'Search')
                )}
              </Button>
            </form>
          </div>

          {/* Categories */}
          <section className="py-6 bg-white relative overflow-hidden">
            <div className="container mx-auto px-2 relative">
              <div className="text-center mb-4">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-pink-600 rounded-full mb-4 shadow-lg">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-primary">{t('services.title', 'Popular Wedding Services')}</h2>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {/* Marriage Mahal */}
                <div className="flex flex-col rounded-xl overflow-hidden bg-white shadow-sm">
                  <button className="w-full h-32 bg-gray-100 focus:outline-none relative" onClick={() => router.push('/vendors?category=marriage-mahal')}>
                    <img src="/mahal.webp" alt="Marriage Mahal" className="object-cover w-full h-full rounded-t-xl" />
                  </button>
                  <button className="p-3 text-sm font-medium text-center border-t" onClick={() => router.push('/vendors?category=marriage-mahal')}>
                    {t('services.marriageMahal', 'Marriage Mahal')}
                  </button>
                </div>

                {/* Photography */}
                <div className="flex flex-col rounded-xl overflow-hidden bg-white shadow-sm">
                  <button className="w-full h-32 bg-gray-100 focus:outline-none relative" onClick={() => router.push('/vendors?category=photography')}>
                    <img src="/photography.webp" alt="Photography" className="object-cover w-full h-full rounded-t-xl" />
                  </button>
                  <button className="p-3 text-sm font-medium text-center border-t" onClick={() => router.push('/vendors?category=photography')}>
                    {t('services.photography', 'Photography')}
                  </button>
                </div>

                {/* Catering */}
                <div className="flex flex-col rounded-xl overflow-hidden bg-white shadow-sm">
                  <button className="w-full h-32 bg-gray-100 focus:outline-none relative" onClick={() => router.push('/vendors?category=catering')}>
                    <img src="/catering.webp" alt="Catering" className="object-cover w-full h-full rounded-t-xl" />
                  </button>
                  <button className="p-3 text-sm font-medium text-center border-t" onClick={() => router.push('/vendors?category=catering')}>
                    {t('services.catering', 'Catering')}
                  </button>
                </div>

                {/* Decoration */}
                <div className="flex flex-col rounded-xl overflow-hidden bg-white shadow-sm">
                  <button className="w-full h-32 bg-gray-100 focus:outline-none relative" onClick={() => router.push('/vendors?category=decoration')}>
                    <img src="/decoration.webp" alt="Decoration" className="object-cover w-full h-full rounded-t-xl" />
                  </button>
                  <button className="p-3 text-sm font-medium text-center border-t" onClick={() => router.push('/vendors?category=decoration')}>
                    {t('services.decoration', 'Decoration')}
                  </button>
                </div>
              </div>
            </div>
          </section>

          {/* Featured Shop Products */}
          <section className="py-6 bg-gradient-to-br from-pink-50 via-white to-purple-50 relative overflow-hidden">
            <div className="container mx-auto px-2 relative">
              <div className="text-center mb-4">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-pink-600 rounded-full mb-4 shadow-lg">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-primary">{t('shop.title', 'Wedding Shopping')}</h2>
              </div>

              <FeaturedShopProducts />
            </div>
          </section>

          {/* Popular Wedding Destinations Ad */}
          <PopularDestinationsAd
            showPromotionalBadges={true}
            showSpecialOffers={true}
            enableRedirects={true}
            redirectToVendors={true}
            onDestinationClick={(cityName, cityData) => {
              console.log('Destination clicked:', cityName, cityData);
            }}
          />

          {/* Newsletter - moved to just above Footer */}
          <section className="py-6 bg-primary">
            <div className="container mx-auto px-2 text-center">
              <h2 className="text-xl font-bold text-primary-foreground mb-3">{t('newsletter.title', 'Get Wedding Updates & Offers')}</h2>
              <p className="text-primary-foreground/80 mb-6 max-w-2xl mx-auto text-base">
                {t('newsletter.subtitle', 'Subscribe your email to get wedding updates, top vendors, and special offers.')}
              </p>
              <div className="max-w-md mx-auto">
                <NewsletterSubscription
                  source="MOBILE_HOMEPAGE"
                  variant="compact"
                  className="[&_input]:bg-white [&_button]:bg-white [&_button]:text-primary [&_button]:hover:bg-gray-100"
                />
              </div>
            </div>
          </section>

        </div>
      </LayoutWrapper>
    </>
  )
}
