# Thirumanam 360 - Comprehensive Web Application Documentation

## 🏗️ **Application Architecture Overview**

### **Technology Stack**
- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **UI Framework**: Shadcn/ui components with Radix UI primitives
- **Backend**: AWS Amplify with GraphQL API
- **Database**: AWS DynamoDB
- **Authentication**: AWS Cognito User Pools
- **Storage**: AWS S3 for media files
- **Payments**: Razorpay integration
- **State Management**: React Context API
- **Styling**: Tailwind CSS with CSS variables for theming
- **Icons**: Lucide React icons
- **Performance**: Next.js optimizations, PWA support

### **Project Structure**
```
├── 🌐 app/                      # Next.js App Router
├── 🧩 components/               # Shared Components
├── 📚 src/                      # Core Source Files
├── ⚙️ amplify/                  # AWS Amplify Backend
├── 🎨 public/                   # Static Assets
├── 🔧 lib/                      # Utility Libraries
├── 🪝 hooks/                    # Custom React Hooks
├── 🌍 contexts/                 # React Contexts
├── 💅 styles/                   # Global Styles
└── 🛠️ utils/                    # Utility Functions
```

## 📱 **Core Application Pages**

### **1. Homepage (`/`)**
**File**: `app/page.tsx`
**Features**:
- Hero carousel with 3 wedding images
- Search functionality with location and service filters
- Featured vendors, venues, and shop products sections
- Popular wedding destinations advertisements
- Newsletter subscription
- Multi-language support (15+ Indian languages)
- SEO optimized with structured data

**Key Components**:
- `HeroCarousel` - Auto-playing image carousel
- `FeaturedShopProducts` - Product showcase
- `PopularDestinationsAd` - City-based vendor filtering
- `NewsletterSubscription` - Email collection

### **2. Vendor Pages**
**Listing Page**: `app/vendors/page.tsx`
**Detail Page**: `app/vendors/[id]/page.tsx`

**Features**:
- Advanced filtering (category, location, price, rating, experience)
- Pagination with server-side data loading
- Real-time search with debouncing
- Category-based browsing (Photography, Catering, Decoration, etc.)
- Vendor profile with gallery, services, reviews
- Contact forms with authentication requirements
- Social media integration
- Favorite/bookmark functionality

**Data Model**:
```typescript
interface VendorData {
  id: string;
  name: string;
  category: string;
  description: string;
  contact: string;
  email: string;
  address: string;
  city: string;
  state: string;
  website: string;
  socialMedia: { facebook, instagram, youtube };
  profilePhoto: string;
  gallery: string[];
  services: Array<{ name, price, description }>;
  experience: string;
  rating: number;
  reviewCount: number;
  verified: boolean;
  featured: boolean;
  priceRange: string;
  specializations: string[];
}
```

### **3. Venue Pages**
**Listing Page**: `app/venues/page.tsx`
**Detail Page**: `app/venues/[id]/page.tsx`

**Features**:
- Venue type filtering (Banquet Hall, Resort, Heritage, Beach Resort)
- Capacity-based search
- Budget range filtering
- Amenity filtering
- Venue booking system with availability checking
- Image galleries with multiple photos
- Location mapping integration
- Package and pricing information

**Data Model**:
```typescript
interface VenueData {
  id: string;
  name: string;
  type: string;
  capacity: string;
  location: string;
  city: string;
  state: string;
  price: string;
  description: string;
  images: string[];
  amenities: string[];
  spaces: VenueSpace[];
  packages: VenuePackage[];
  socialMedia: SocialMedia;
  rating: number;
  reviewCount: number;
  verified: boolean;
  featured: boolean;
}
```

### **4. Shop/E-commerce Pages**
**Listing Page**: `app/shop/page.tsx`
**Product Detail**: `app/shop/[id]/page.tsx`

**Features**:
- Product catalog with categories
- Shopping cart functionality
- Wishlist/favorites
- Product reviews and ratings
- Image zoom and gallery
- Size and color variants
- Inventory management
- Razorpay payment integration
- Order tracking

**Data Model**:
```typescript
interface ShopData {
  id: string;
  name: string;
  category: string;
  description: string;
  price: number;
  originalPrice: number;
  discount: number;
  images: string[];
  specifications: ShopSpecifications;
  inStock: boolean;
  quantity: number;
  rating: number;
  reviewCount: number;
  tags: string[];
  brand: string;
  sku: string;
}
```

### **5. Authentication System**
**Login**: `app/login/page.tsx`
**Register**: `app/register/page.tsx`
**Vendor Signup**: `app/vendor-signup/page.tsx`

**Features**:
- Multi-method authentication (Email/Password, Mobile OTP)
- Customer and Vendor account types
- AWS Cognito integration
- Role-based access control
- Password reset functionality
- Email verification
- Mobile OTP verification
- Social login integration ready

**User Types**:
- **Customer**: Regular users for booking and shopping
- **Vendor**: Business accounts for service providers
- **Admin**: Platform administrators
- **Super Admin**: System administrators

## 🎛️ **Dashboard System**

### **Layout**: `app/dashboard/layout.tsx`
**Features**:
- Role-based sidebar navigation
- User profile display
- Responsive mobile menu
- Logout functionality
- Search within dashboard

### **Customer Dashboard**
- Profile management
- Booking history
- Order tracking
- Inquiry management
- Favorites list
- Blog creation

### **Vendor Dashboard**
- Business profile management
- Service/product catalog
- Order management
- Booking calendar
- Customer inquiries
- Review management
- Analytics and insights

### **Admin Dashboard**
- User management
- Content moderation
- Vendor verification
- Order oversight
- Newsletter management
- Platform analytics
- System configuration

## 🧩 **Component Architecture**

### **Design System** (`components/ui/`)
Based on Shadcn/ui with Radix UI primitives:

**Core Components**:
- `Button` - Multiple variants (default, outline, secondary, ghost, destructive)
- `Card` - Container component with header, content, footer
- `Input` - Form input with validation states
- `Badge` - Status indicators and tags
- `Select` - Dropdown selection component
- `Tabs` - Tabbed interface component
- `Dialog` - Modal dialogs and overlays
- `Form` - Form handling with validation

**Color System**:
```css
:root {
  --primary: 357 70% 21%;        /* Maroon #610f13 */
  --secondary: 90 34% 45%;       /* Leaf green */
  --accent: 44 89% 62%;          /* Gold #F6C244 */
  --background: 0 0% 100%;       /* White */
  --foreground: 0 0% 3.9%;       /* Dark text */
}
```

### **Business Logic Components**
- `AuthenticatedContactVendor` - Vendor contact forms
- `FavoriteButton` - Bookmark functionality
- `AddToCartButton` - E-commerce cart actions
- `EntityReviews` - Review system
- `QuickInquiryForm` - Lead generation forms

## 🔧 **Services Architecture**

### **Core Services** (`lib/services/`)

**1. VendorService** (`vendorService.ts`)
- CRUD operations for vendor profiles
- Search and filtering
- Image upload handling
- Status management (active, pending, suspended)

**2. VenueService** (`venueService.ts`)
- Venue management
- Availability checking
- Booking coordination
- Package management

**3. ShopService** (`shopService.ts`)
- Product catalog management
- Inventory tracking
- Category management
- Price calculations

**4. CartService** (`cartService.ts`)
- Shopping cart operations
- Item quantity management
- Cart persistence
- Checkout preparation

**5. BookingService** (`bookingService.ts`)
- Appointment scheduling
- Availability validation
- Conflict resolution
- Status tracking

**6. PaymentService** (`paymentService.ts`)
- Razorpay integration
- Payment processing
- Transaction tracking
- Refund handling

**7. ProfileService** (`profileService.ts`)
- User profile management
- Business information
- Preferences handling
- Role assignment

### **Authentication Services**
- `AuthContext` - Global authentication state
- `hybridMobileAuth` - Mobile OTP handling
- `authRouting` - Role-based redirects
- `userRoleService` - Permission management

## 📊 **Data Models & GraphQL Schema**

### **Core Entities**
```graphql
type Vendor {
  id: ID!
  userId: ID!
  name: String!
  category: String!
  description: String
  contact: String!
  email: String!
  city: String!
  state: String!
  rating: Float
  reviewCount: Int
  verified: Boolean
  featured: Boolean
  status: String!
}

type Venue {
  id: ID!
  userId: ID!
  name: String!
  type: String!
  capacity: String!
  location: String!
  city: String!
  state: String!
  price: String!
  amenities: [String]
  verified: Boolean
  featured: Boolean
}

type Shop {
  id: ID!
  userId: ID!
  name: String!
  category: String!
  price: Float!
  description: String
  images: [String]
  inStock: Boolean!
  quantity: Int!
  rating: Float
  reviewCount: Int
}

type UserProfile {
  id: ID!
  userId: ID!
  firstName: String
  lastName: String
  email: String!
  phone: String
  isVendor: Boolean
  isAdmin: Boolean
  businessInfo: BusinessInfo
  preferences: UserPreferences
}
```

## 🚀 **Performance Optimizations**

### **Next.js Optimizations**
- App Router for improved performance
- Image optimization with Next/Image
- Code splitting and lazy loading
- Static generation where possible
- Bundle analysis and optimization

### **Caching Strategy**
- API response caching
- Image caching with CDN
- Browser caching headers
- Service worker for offline support

### **Database Optimization**
- DynamoDB GSI for efficient queries
- Pagination for large datasets
- Connection pooling
- Query optimization

## 🔒 **Security Features**

### **Authentication Security**
- AWS Cognito User Pools
- JWT token validation
- Role-based access control
- Session management
- Password policies

### **Data Protection**
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection
- Rate limiting

### **API Security**
- GraphQL query depth limiting
- Authentication middleware
- Authorization checks
- Audit logging

## 📱 **Mobile Responsiveness**

### **Responsive Design**
- Mobile-first approach
- Tailwind CSS breakpoints
- Touch-friendly interfaces
- Optimized images for mobile
- Progressive Web App features

### **PWA Features**
- Service worker implementation
- Offline functionality
- App-like experience
- Push notifications ready
- Install prompts

## 🌐 **Internationalization**

### **Multi-language Support**
- 15+ Indian languages supported
- React i18next integration
- Dynamic language switching
- RTL support ready
- Cultural adaptations

### **Localization Features**
- Currency formatting (INR)
- Date/time formatting
- Number formatting
- Address formats
- Cultural preferences

## 📈 **Analytics & Monitoring**

### **Performance Monitoring**
- Web Vitals tracking
- Error boundary implementation
- Performance metrics
- User behavior analytics
- Conversion tracking

### **Business Analytics**
- Vendor performance metrics
- Booking conversion rates
- Revenue tracking
- User engagement metrics
- Geographic analytics

## 🔄 **Deployment & DevOps**

### **Build Configuration**
- TypeScript compilation
- Tailwind CSS processing
- Bundle optimization
- Environment configuration
- Console log removal in production

### **AWS Infrastructure**
- Amplify hosting
- CloudFront CDN
- S3 storage
- DynamoDB database
- Cognito authentication
- Lambda functions

This documentation provides a comprehensive overview of the Thirumanam 360 web application architecture, features, and implementation details.
