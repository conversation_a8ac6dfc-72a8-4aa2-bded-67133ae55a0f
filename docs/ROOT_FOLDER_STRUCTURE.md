# Thirumanam 360 - Complete Root Folder Structure (Excluding Apps)

## 📁 **Root Directory Structure**

```
Thirumanam/
├── 📄 README.md                                    # Project documentation
├── 📄 COMPLETE_MOBILE_API_INTEGRATION.md          # Mobile API integration guide
├── 📄 MOBILE_API_INTEGRATION_SUMMARY.md           # API integration summary
├── 📄 package.json                                # Node.js dependencies
├── 📄 package-lock.json                           # Dependency lock file
├── 📄 pnpm-lock.yaml                              # PNPM lock file
├── 📄 tsconfig.json                               # TypeScript configuration
├── 📄 tailwind.config.ts                          # Tailwind CSS configuration
├── 📄 postcss.config.mjs                          # PostCSS configuration
├── 📄 next.config.js                              # Next.js configuration
├── 📄 next-i18next.config.js                      # Internationalization config
├── 📄 components.json                             # Shadcn/ui components config
├── 📄 next-env.d.ts                               # Next.js TypeScript definitions
├── 📄 dev-fast.sh                                 # Development script
├── 📄 create-clean-punjabi.js                     # Language utility
├── 📄 fix-missing-language-support.js             # Language fix script
├── 📄 update-all-languages.js                     # Language update script
├── 📄 update-destinations-translations.js         # Destination translations
├── 📄 update-missing-sections-translations.js     # Section translations
│
├── 🌐 app/                                        # Next.js App Router
│   ├── 📄 layout.tsx                             # Root layout
│   ├── 📄 page.tsx                               # Homepage
│   ├── 📄 globals.css                            # Global styles
│   ├── 📄 loading.tsx                            # Global loading component
│   ├── 📄 error.tsx                              # Global error component
│   ├── 📄 not-found.tsx                          # 404 page
│   ├── 📄 robots.ts                              # SEO robots configuration
│   ├── 📄 sitemap.ts                             # SEO sitemap generation
│   ├── 📄 sitemap.xml                            # Static sitemap
│   ├── 📄 order-placed.tsx                       # Order confirmation page
│   ├── 📄 page.tsx.bak                           # Backup homepage
│   │
│   ├── 📁 vendors/                               # Vendor pages
│   │   ├── 📄 page.tsx                          # Vendor listing
│   │   └── 📁 [id]/                             # Dynamic vendor pages
│   │
│   ├── 📁 venues/                                # Venue pages
│   │   ├── 📄 page.tsx                          # Venue listing
│   │   └── 📁 [id]/                             # Dynamic venue pages
│   │
│   ├── 📁 shop/                                  # E-commerce shop
│   │   ├── 📄 page.tsx                          # Product listing
│   │   └── 📁 [id]/                             # Product detail pages
│   │
│   ├── 📁 dashboard/                             # User dashboards
│   ├── 📁 login/                                 # Authentication
│   ├── 📁 register/                              # User registration
│   ├── 📁 vendor-signup/                         # Vendor registration
│   ├── 📁 admin/                                 # Admin pages
│   ├── 📁 blog/                                  # Blog system
│   ├── 📁 booking/                               # Booking system
│   ├── 📁 cart/                                  # Shopping cart
│   ├── 📁 checkout/                              # Checkout process
│   ├── 📁 orders/                                # Order management
│   ├── 📁 payment/                               # Payment processing
│   ├── 📁 planning/                              # Wedding planning tools
│   ├── 📁 favorites/                             # User favorites
│   ├── 📁 contact/                               # Contact pages
│   ├── 📁 help/                                  # Help & support
│   ├── 📁 privacy/                               # Privacy policy
│   ├── 📁 terms/                                 # Terms of service
│   ├── 📁 reviews/                               # Review system
│   ├── 📁 newsletter/                            # Newsletter management
│   ├── 📁 offers/                                # Special offers
│   ├── 📁 photos/                                # Photo galleries
│   ├── 📁 real-weddings/                         # Real wedding stories
│   ├── 📁 community/                             # Community features
│   ├── 📁 download-app/                          # Mobile app download
│   └── 📁 api/                                   # API routes
│
├── 🧩 components/                                 # Shared React Components
│   ├── 📄 header.tsx                            # Main header component
│   ├── 📄 footer.tsx                            # Main footer component
│   ├── 📄 theme-provider.tsx                    # Theme provider
│   ├── 📄 top-header.tsx                        # Top header bar
│   ├── 📄 simple-top-header.tsx                 # Simplified top header
│   ├── 📄 layout-wrapper.tsx                    # Layout wrapper
│   ├── 📄 client-only.tsx                       # Client-side only wrapper
│   ├── 📄 translation-wrapper.tsx               # Translation wrapper
│   ├── 📄 multi-language-header-test.tsx        # Language testing
│   ├── 📄 state-specific-vendor-demo.tsx        # State vendor demo
│   ├── 📄 language-demo.tsx                     # Language demo
│   ├── 📄 image-upload-demo.tsx                 # Image upload demo
│   ├── 📄 review-system-test.tsx                # Review system test
│   ├── 📄 review-type-selector.tsx              # Review type selector
│   ├── 📄 platform-review-form.tsx              # Platform review form
│   ├── 📄 entity-review-form.tsx                # Entity review form
│   ├── 📄 entity-reviews.tsx                    # Entity reviews display
│   ├── 📄 user-reviews-dashboard.tsx            # User reviews dashboard
│   ├── 📄 vendor-reviews-dashboard.tsx          # Vendor reviews dashboard
│   ├── 📄 admin-review-management.tsx           # Admin review management
│   ├── 📄 admin-reviews-dashboard.tsx           # Admin reviews dashboard
│   │
│   ├── 📁 ui/                                   # Design system components
│   │   ├── 📄 button.tsx                       # Button component
│   │   ├── 📄 card.tsx                         # Card component
│   │   ├── 📄 input.tsx                        # Input component
│   │   ├── 📄 badge.tsx                        # Badge component
│   │   ├── 📄 select.tsx                       # Select dropdown
│   │   ├── 📄 tabs.tsx                         # Tabs component
│   │   ├── 📄 dialog.tsx                       # Dialog/modal
│   │   ├── 📄 form.tsx                         # Form components
│   │   ├── 📄 command.tsx                      # Command palette
│   │   └── 📄 ...                              # Other UI components
│   │
│   ├── 📁 auth/                                 # Authentication components
│   ├── 📁 dashboard/                            # Dashboard components
│   ├── 📁 booking/                              # Booking components
│   ├── 📁 admin/                                # Admin components
│   ├── 📁 analytics/                            # Analytics components
│   ├── 📁 mobile/                               # Mobile-specific components
│   ├── 📁 lazy/                                 # Lazy-loaded components
│   ├── 📁 newsletter/                           # Newsletter components
│   ├── 📁 seo/                                  # SEO components
│   └── 📁 performance/                          # Performance components
│
├── 📚 src/                                       # Core Source Files
│   ├── 📄 amplifyconfiguration.json            # Amplify configuration
│   ├── 📄 aws-exports.js                       # AWS exports
│   │
│   ├── 📁 graphql/                             # GraphQL operations
│   │   ├── 📄 mutations.ts                     # GraphQL mutations
│   │   ├── 📄 queries.ts                       # GraphQL queries
│   │   └── 📄 subscriptions.ts                 # GraphQL subscriptions
│   │
│   ├── 📁 models/                              # Data models
│   ├── 📁 components/                          # Source components
│   ├── 📁 screens/                             # Screen components
│   ├── 📁 services/                            # Business services
│   ├── 📁 hooks/                               # Custom hooks
│   ├── 📁 utils/                               # Utility functions
│   └── 📁 config/                              # Configuration files
│
├── ⚙️ amplify/                                  # AWS Amplify Backend
│   ├── 📄 README.md                            # Amplify documentation
│   ├── 📄 cli.json                             # Amplify CLI config
│   ├── 📄 team-provider-info.json              # Team provider info
│   ├── 📁 backend/                             # Backend configuration
│   ├── 📁 hooks/                               # Amplify hooks
│   └── 📁 #current-cloud-backend/              # Current backend state
│
├── 🎨 public/                                   # Static Assets
│   ├── 📄 manifest.json                        # PWA manifest
│   ├── 📄 sw.js                                # Service worker
│   ├── 📄 workbox-67e23458.js                  # Workbox service worker
│   ├── 📄 Thirumanam360-logo.png               # Main logo
│   ├── 📄 Thirumanam360_favicon.png            # Favicon
│   ├── 📄 Thirumanam360_logo.svg               # SVG logo
│   ├── 📄 hero_image_1.webp                    # Hero image 1
│   ├── 📄 hero_image_2.webp                    # Hero image 2
│   ├── 📄 hero_image_3.webp                    # Hero image 3
│   ├── 📄 placeholder.jpg                      # Placeholder image
│   ├── 📄 placeholder.svg                      # Placeholder SVG
│   ├── 📄 placeholder-user.jpg                 # User placeholder
│   ├── 📄 placeholder-logo.png                 # Logo placeholder
│   ├── 📄 placeholder-logo.svg                 # Logo placeholder SVG
│   │
│   ├── 📁 locales/                             # Internationalization files
│   │   ├── 📁 en/                              # English translations
│   │   ├── 📁 ta/                              # Tamil translations
│   │   ├── 📁 hi/                              # Hindi translations
│   │   ├── 📁 te/                              # Telugu translations
│   │   ├── 📁 kn/                              # Kannada translations
│   │   ├── 📁 ml/                              # Malayalam translations
│   │   ├── 📁 mr/                              # Marathi translations
│   │   ├── 📁 gu/                              # Gujarati translations
│   │   ├── 📁 bn/                              # Bengali translations
│   │   ├── 📁 pa/                              # Punjabi translations
│   │   ├── 📁 or/                              # Odia translations
│   │   ├── 📁 as/                              # Assamese translations
│   │   ├── 📁 ur/                              # Urdu translations
│   │   ├── 📁 sa/                              # Sanskrit translations
│   │   └── 📁 ne/                              # Nepali translations
│   │
│   └── 📁 images/                              # Category & service images
│       ├── 📄 photographer.webp                # Photography services
│       ├── 📄 catering.webp                    # Catering services
│       ├── 📄 decorators.webp                  # Decoration services
│       ├── 📄 bride_makeup.webp                # Makeup services
│       ├── 📄 dj_music.webp                    # Music services
│       ├── 📄 event_organizers.webp            # Event planning
│       ├── 📄 transportations.webp             # Transportation
│       ├── 📄 flower_shops.webp                # Flower shops
│       ├── 📄 dress_store.webp                 # Dress stores
│       ├── 📄 wedding_jewels.webp              # Jewelry
│       ├── 📄 return_gift.webp                 # Return gifts
│       ├── 📄 snacks_shop.webp                 # Snacks
│       ├── 📄 Invitations.webp                 # Invitations
│       ├── 📄 astrologer.webp                  # Astrology
│       ├── 📄 nadasvaram.webp                  # Traditional music
│       ├── 📄 mahal.webp                       # Venues
│       ├── 📄 mahal_hero.webp                  # Venue hero
│       ├── 📄 iyer_image.webp                  # Traditional services
│       ├── 📄 chennai_image.jpg                # Chennai city
│       ├── 📄 coimbatore_image.jpg             # Coimbatore city
│       ├── 📄 madurai_image.jpg                # Madurai city
│       └── 📄 tirunelveli_image.jpg            # Tirunelveli city
│
├── 🔧 lib/                                      # Utility Libraries
│   ├── 📄 utils.ts                             # General utilities
│   ├── 📄 aws-config.ts                        # AWS configuration
│   ├── 📄 amplify-singleton.ts                 # Amplify singleton
│   ├── 📄 i18n.ts                              # Internationalization
│   ├── 📄 toast.ts                             # Toast notifications
│   ├── 📄 icon-imports.ts                      # Icon imports
│   ├── 📄 image-optimization.ts                # Image optimization
│   ├── 📄 lazy-load.tsx                        # Lazy loading
│   ├── 📄 otp-service.ts                       # OTP service
│   ├── 📄 storage-utils.ts                     # Storage utilities
│   ├── 📄 performance-fixes.ts                 # Performance fixes
│   ├── 📄 simple-performance-fix.ts            # Simple performance fix
│   ├── 📄 cleanup-indexeddb.ts                 # IndexedDB cleanup
│   ├── 📄 disable-indexeddb.ts                 # Disable IndexedDB
│   ├── 📄 disable-console.ts                   # Console disabling
│   ├── 📄 disable-console-simple.ts            # Simple console disable
│   ├── 📄 build-console-remover.js             # Build console remover
│   ├── 📄 webpack-console-plugin.js            # Webpack console plugin
│   │
│   ├── 📁 services/                            # Business logic services
│   │   ├── 📄 vendorService.ts                 # Vendor operations
│   │   ├── 📄 venueService.ts                  # Venue operations
│   │   ├── 📄 shopService.ts                   # Shop operations
│   │   ├── 📄 cartService.ts                   # Cart operations
│   │   ├── 📄 bookingService.ts                # Booking operations
│   │   ├── 📄 paymentService.ts                # Payment operations
│   │   ├── 📄 profileService.ts                # Profile operations
│   │   └── 📄 adminContentService.ts           # Admin content service
│   │
│   ├── 📁 utils/                               # Utility functions
│   ├── 📁 config/                              # Configuration files
│   ├── 📁 analytics/                           # Analytics utilities
│   ├── 📁 performance/                         # Performance utilities
│   ├── 📁 debug/                               # Debug utilities
│   └── 📁 test/                                # Test utilities
│
├── 🪝 hooks/                                    # Custom React Hooks
│   ├── 📄 use-toast.ts                         # Toast hook
│   ├── 📄 use-mobile.tsx                       # Mobile detection hook
│   ├── 📄 use-safe-translation.ts              # Safe translation hook
│   ├── 📄 useNewsletter.ts                     # Newsletter hook
│   └── 📄 useWelcomeEmail.ts                   # Welcome email hook
│
├── 🌍 contexts/                                 # React Contexts
│   ├── 📄 AuthContext.tsx                      # Authentication context
│   └── 📄 StateContext.tsx                     # State management context
│
├── 💅 styles/                                   # Global Styles
│   ├── 📄 globals.css                          # Global CSS styles
│   └── 📄 nprogress.css                        # Progress bar styles
│
├── 🛠️ utils/                                    # Utility Functions
│   └── 📄 state-specific-vendors.ts            # State-specific utilities
│
├── 📁 navigation/                               # Navigation configuration
│
├── 📁 scripts/                                  # Build & utility scripts
│   ├── 📄 browserSeedData.js                   # Browser seed data
│   ├── 📄 create-blogs-node.js                 # Blog creation script
│   ├── 📄 create-test-blogs-simple.js          # Simple blog test
│   ├── 📄 create-test-blogs.js                 # Blog test script
│   ├── 📄 dev-optimize.js                      # Development optimization
│   ├── 📄 seedTestData.js                      # Test data seeding
│   ├── 📄 setup-console-removal.js             # Console removal setup
│   └── 📄 updateUserRole.ts                    # User role update
│
├── 📁 packages/                                 # Shared packages
│   └── 📁 shared/                              # Shared package
│       └── 📁 src/                             # Shared source
│           ├── 📄 index.ts                     # Package exports
│           ├── 📁 types/                       # Shared types
│           ├── 📁 services/                    # Shared services
│           ├── 📁 utils/                       # Shared utilities
│           └── 📁 hooks/                       # Shared hooks
│
├── 📁 docs/                                     # Documentation
│   ├── 📄 APPLICATION_OVERVIEW.md              # Application overview
│   ├── 📄 BUSINESS_LOGIC_FLOWS.md              # Business logic flows
│   ├── 📄 COMPREHENSIVE_WEB_APPLICATION_DOCUMENTATION.md # Web app docs
│   ├── 📄 CONCURRENT_BOOKING_MANAGEMENT.md     # Booking management
│   ├── 📄 FIRST_CLICK_PERFORMANCE_FIX.md       # Performance fixes
│   ├── 📄 INVENTORY_MANAGEMENT.md              # Inventory management
│   ├── 📄 MOBILE_APP_COMPLETE_IMPLEMENTATION_GUIDE.md # Mobile guide
│   ├── 📄 MOBILE_APP_IMPLEMENTATION_SUMMARY.md # Mobile summary
│   ├── 📄 MOBILE_APP_PHASES_DETAILED_IMPLEMENTATION.md # Mobile phases
│   ├── 📄 MOBILE_APP_REVIEW_ANALYSIS.md        # Mobile review analysis
│   ├── 📄 MOBILE_BUNDLING_FIX.md               # Mobile bundling fix
│   ├── 📄 MOBILE_OTP_DEPLOYMENT_FIX.md         # Mobile OTP fix
│   ├── 📄 PAGES_AND_VIEWS_DETAILED.md          # Pages documentation
│   ├── 📄 SEO_IMPLEMENTATION.md                # SEO implementation
│   ├── 📄 SWC_CONSOLE_REMOVAL.md               # Console removal
│   ├── 📄 UNCONFIRMED_USER_FLOW.md             # User confirmation flow
│   ├── 📄 USER_INFO_DISPLAY.md                 # User info display
│   └── 📄 WELCOME_EMAIL_SETUP.md               # Welcome email setup
│
└── 📁 node_modules/                             # Dependencies (auto-generated)
```

## 📊 **Key Statistics**

- **Total Directories**: 50+ organized folders
- **Configuration Files**: 15+ config files
- **Documentation Files**: 20+ comprehensive docs
- **Script Files**: 10+ automation scripts
- **Language Support**: 15+ Indian languages
- **Image Assets**: 25+ optimized images
- **Service Files**: 10+ business logic services
- **Component Categories**: 12+ component types
- **Utility Libraries**: 20+ utility functions

## 🔧 **Configuration Overview**

### **Build & Development**
- `package.json` - Dependencies and scripts
- `tsconfig.json` - TypeScript configuration
- `tailwind.config.ts` - Styling configuration
- `next.config.js` - Next.js configuration
- `postcss.config.mjs` - CSS processing

### **AWS & Backend**
- `amplify/` - AWS Amplify backend configuration
- `src/amplifyconfiguration.json` - Amplify config
- `src/aws-exports.js` - AWS exports

### **Internationalization**
- `next-i18next.config.js` - i18n configuration
- `public/locales/` - Translation files for 15+ languages

### **PWA & Performance**
- `public/manifest.json` - PWA manifest
- `public/sw.js` - Service worker
- Performance optimization scripts in `lib/`

This structure represents a comprehensive, production-ready wedding platform with extensive features, multi-language support, and robust architecture.
